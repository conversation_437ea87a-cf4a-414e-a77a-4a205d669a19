# coding=utf-8
"""
API测试场景执行器

Created on 2025-09-17
@author: AI Assistant
"""

import time
import json
import asyncio
import concurrent.futures
from typing import Dict, List, Any, Optional
from datetime import datetime

from django.utils import timezone
from django.db import transaction

from teamvision.project.models import (
    ApiTestScene, ApiTestSceneStep, ApiTestSceneHistory, 
    ApiTestSceneStepRecord, ApiTestEnvironment, ApiTestCase
)
from business.project.api_test_executor import ApiTestExecutor, EnvironmentProcessor
from utils.simple_logger import SimpleLogger


class ApiTestSceneExecutor:
    """API测试场景执行器"""

    def __init__(self, scene: ApiTestScene, environment_id: Optional[int] = None):
        self.scene = scene
        self.environment = self._load_environment(environment_id)
        self.environment_id = environment_id
        self.scene_variables = {}
        self.scene_history = None
        self.processor = EnvironmentProcessor(self.environment)

    def _load_environment(self, environment_id: Optional[int]) -> Optional[ApiTestEnvironment]:
        """加载环境变量"""
        if environment_id:
            try:
                return ApiTestEnvironment.objects.get(id=environment_id, IsActive=True)
            except ApiTestEnvironment.DoesNotExist:
                SimpleLogger.warning(f"Environment {environment_id} not found")
        return None

    def execute(self, executor_id: int) -> Dict[str, Any]:
        """执行场景"""
        try:
            # 创建执行历史记录
            self.scene_history = self._create_scene_history(executor_id)
            
            # 初始化场景变量
            self.scene_variables.update(self.scene.global_variables)
            
            # 获取启用的步骤
            steps = ApiTestSceneStep.objects.filter(
                scene=self.scene,
                IsActive=True,
                is_enabled=True
            ).order_by('step_order')

            # 更新总步骤数
            self.scene_history.total_steps = steps.count()
            self.scene_history.save()

            if self.scene.is_parallel:
                self._execute_parallel(steps)
            else:
                self._execute_sequential(steps)

            # 完成执行
            self._finalize_execution()

            return {
                'history_id': self.scene_history.id,
                'status': self.scene_history.status,
                'is_success': self.scene_history.is_success,
                'total_steps': self.scene_history.total_steps,
                'success_steps': self.scene_history.success_steps,
                'failed_steps': self.scene_history.failed_steps,
                'total_time': self.scene_history.total_time
            }

        except Exception as e:
            self._handle_execution_error(str(e))
            raise

    def _create_scene_history(self, executor_id: int) -> ApiTestSceneHistory:
        """创建场景执行历史记录"""
        scene_snapshot = {
            'id': self.scene.id,
            'name': self.scene.name,
            'description': self.scene.description,
            'global_variables': self.scene.global_variables,
            'global_headers': self.scene.global_headers,
            'pre_script': self.scene.pre_script,
            'post_script': self.scene.post_script,
            'assertions': self.scene.assertions,
            'settings': self.scene.settings,
            'is_parallel': self.scene.is_parallel
        }

        return ApiTestSceneHistory.objects.create(
            project_id=self.scene.project_id,
            scene=self.scene,
            scene_snapshot=scene_snapshot,
            start_time=timezone.now(),
            status='running',
            environment_id=self.environment_id,
            environment_name=self.environment.name if self.environment else None,
            executor=executor_id
        )

    def _execute_sequential(self, steps):
        """顺序执行步骤"""
        for step in steps:
            if not self._should_continue_execution():
                break
            
            self._execute_single_step(step)

    def _execute_parallel(self, steps):
        """并行执行步骤"""
        with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
            futures = []
            for step in steps:
                if not self._should_continue_execution():
                    break
                future = executor.submit(self._execute_single_step, step)
                futures.append(future)
            
            # 等待所有步骤完成
            concurrent.futures.wait(futures)

    def _execute_single_step(self, step: ApiTestSceneStep):
        """执行单个步骤"""
        step_record = self._create_step_record(step)
        
        try:
            step_record.start_time = timezone.now()
            step_record.save()

            if step.step_type == 'api':
                result = self._execute_api_step(step, step_record)
            elif step.step_type == 'wait':
                result = self._execute_wait_step(step, step_record)
            elif step.step_type == 'controller':
                result = self._execute_controller_step(step, step_record)
            elif step.step_type == 'case':
                result = self._execute_case_step(step, step_record)
            else:
                raise ValueError(f"不支持的步骤类型: {step.step_type}")

            # 更新步骤记录
            step_record.end_time = timezone.now()
            step_record.execution_time = int(
                (step_record.end_time - step_record.start_time).total_seconds() * 1000
            )
            step_record.is_success = result.get('is_success', True)
            step_record.status = 'completed' if step_record.is_success else 'failed'
            
            if not step_record.is_success:
                step_record.error_message = result.get('error_message', '')

            step_record.save()

            # 更新场景统计
            with transaction.atomic():
                if step_record.is_success:
                    self.scene_history.success_steps += 1
                else:
                    self.scene_history.failed_steps += 1
                self.scene_history.save()

        except Exception as e:
            step_record.end_time = timezone.now()
            step_record.execution_time = int(
                (step_record.end_time - step_record.start_time).total_seconds() * 1000
            )
            step_record.status = 'failed'
            step_record.is_success = False
            step_record.error_message = str(e)
            step_record.save()

            # 更新场景统计
            with transaction.atomic():
                self.scene_history.failed_steps += 1
                self.scene_history.save()

            SimpleLogger.exception(e)

    def _create_step_record(self, step: ApiTestSceneStep) -> ApiTestSceneStepRecord:
        """创建步骤执行记录"""
        step_snapshot = {
            'id': step.id,
            'step_name': step.step_name,
            'description': step.description,
            'step_type': step.step_type,
            'method': step.method,
            'url': step.url,
            'headers': step.headers,
            'query_params': step.query_params,
            'body_type': step.body_type,
            'body_data': step.body_data,
            'wait_time': step.wait_time,
            'condition': step.condition,
            'operator': step.operator,
            'expected_value': step.expected_value,
            'test_case_id': step.test_case.id if step.test_case else None,
            'variable_extracts': step.variable_extracts,
            'step_assertions': step.step_assertions
        }

        return ApiTestSceneStepRecord.objects.create(
            scene_history=self.scene_history,
            step=step,
            step_snapshot=step_snapshot,
            step_order=step.step_order,
            step_name=step.step_name,
            step_type=step.step_type,
            status='running'
        )

    def _execute_api_step(self, step: ApiTestSceneStep, step_record: ApiTestSceneStepRecord) -> Dict[str, Any]:
        """执行API步骤"""
        try:
            # 处理变量替换
            processed_url = self._process_variables(step.url)
            processed_headers = self._process_dict_variables(step.headers)
            processed_query_params = self._process_dict_variables(step.query_params)
            processed_body_data = self._process_dict_variables(step.body_data)

            # 创建临时测试用例对象
            temp_case = ApiTestCase(
                name=step.step_name,
                method=step.method,
                url=processed_url,
                headers=processed_headers,
                query_params=processed_query_params,
                body_type=step.body_type,
                body_data=processed_body_data,
                test_assertions=step.step_assertions
            )

            # 执行API请求
            executor = ApiTestExecutor(temp_case, self.environment_id)
            result = executor.execute()

            # 保存请求响应信息
            step_record.request_snapshot = executor.get_request_snapshot()
            step_record.response_status = result.get('status_code')
            step_record.response_headers = result.get('headers', {})
            step_record.response_body = result.get('body', '')
            step_record.response_time = result.get('response_time', 0)
            step_record.response_size = result.get('response_size', 0)
            step_record.assertion_results = result.get('test_results', [])

            # 提取变量
            if step.variable_extracts:
                extracted_vars = self._extract_variables(result, step.variable_extracts)
                step_record.extracted_variables = extracted_vars
                self.scene_variables.update(extracted_vars)

            step_record.save()
            return result

        except Exception as e:
            return {
                'is_success': False,
                'error_message': str(e)
            }

    def _execute_wait_step(self, step: ApiTestSceneStep, step_record: ApiTestSceneStepRecord) -> Dict[str, Any]:
        """执行等待步骤"""
        try:
            wait_time = step.wait_time or 0
            time.sleep(wait_time / 1000.0)  # 转换为秒
            
            return {
                'is_success': True,
                'wait_time': wait_time
            }
        except Exception as e:
            return {
                'is_success': False,
                'error_message': str(e)
            }

    def _execute_controller_step(self, step: ApiTestSceneStep, step_record: ApiTestSceneStepRecord) -> Dict[str, Any]:
        """执行控制器步骤"""
        try:
            # 简单的条件判断实现
            condition = self._process_variables(step.condition or '')
            operator = step.operator or '=='
            expected_value = self._process_variables(step.expected_value or '')
            
            # 这里可以实现更复杂的条件判断逻辑
            # 目前只是简单返回成功
            return {
                'is_success': True,
                'condition_result': True
            }
        except Exception as e:
            return {
                'is_success': False,
                'error_message': str(e)
            }

    def _execute_case_step(self, step: ApiTestSceneStep, step_record: ApiTestSceneStepRecord) -> Dict[str, Any]:
        """执行测试用例步骤"""
        try:
            if not step.test_case:
                raise ValueError("测试用例步骤必须关联一个测试用例")

            # 执行关联的测试用例
            executor = ApiTestExecutor(step.test_case, self.environment_id)
            result = executor.execute()

            # 保存执行结果
            step_record.request_snapshot = executor.get_request_snapshot()
            step_record.response_status = result.get('status_code')
            step_record.response_headers = result.get('headers', {})
            step_record.response_body = result.get('body', '')
            step_record.response_time = result.get('response_time', 0)
            step_record.response_size = result.get('response_size', 0)
            step_record.assertion_results = result.get('test_results', [])

            step_record.save()
            return result

        except Exception as e:
            return {
                'is_success': False,
                'error_message': str(e)
            }

    def _process_variables(self, text: str) -> str:
        """处理文本中的变量替换"""
        if not text or not isinstance(text, str):
            return text

        # 处理场景变量 {{variable_name}}
        for var_name, var_value in self.scene_variables.items():
            placeholder = f"{{{{{var_name}}}}}"
            text = text.replace(placeholder, str(var_value))

        # 处理环境变量
        if self.environment:
            for var_name, var_value in self.environment.variables.items():
                placeholder = f"{{{{{var_name}}}}}"
                text = text.replace(placeholder, str(var_value))

        return text

    def _process_dict_variables(self, data: Dict) -> Dict:
        """处理字典中的变量替换"""
        if not data:
            return data

        result = {}
        for key, value in data.items():
            if isinstance(value, str):
                result[key] = self._process_variables(value)
            elif isinstance(value, dict):
                result[key] = self._process_dict_variables(value)
            else:
                result[key] = value

        return result

    def _extract_variables(self, response_result: Dict, extract_configs: List[Dict]) -> Dict[str, Any]:
        """从响应中提取变量"""
        extracted = {}

        try:
            response_body = response_result.get('body', '')
            response_headers = response_result.get('headers', {})

            for config in extract_configs:
                var_name = config.get('name')
                extract_type = config.get('type', 'jsonpath')  # jsonpath, regex, header
                expression = config.get('expression')

                if not var_name or not expression:
                    continue

                if extract_type == 'header':
                    # 从响应头提取
                    extracted[var_name] = response_headers.get(expression)
                elif extract_type == 'jsonpath':
                    # 从JSON响应体提取（简化实现）
                    try:
                        if isinstance(response_body, str):
                            response_json = json.loads(response_body)
                        else:
                            response_json = response_body

                        # 简单的JSONPath实现，支持基本的点号访问
                        value = response_json
                        for key in expression.split('.'):
                            if key.startswith('[') and key.endswith(']'):
                                # 数组索引
                                index = int(key[1:-1])
                                value = value[index]
                            else:
                                value = value.get(key)

                            if value is None:
                                break

                        extracted[var_name] = value
                    except (json.JSONDecodeError, KeyError, IndexError, TypeError):
                        extracted[var_name] = None

        except Exception as e:
            SimpleLogger.warning(f"变量提取失败: {e}")

        return extracted

    def _should_continue_execution(self) -> bool:
        """检查是否应该继续执行"""
        # 刷新历史记录状态
        self.scene_history.refresh_from_db()
        return self.scene_history.status == 'running'

    def _finalize_execution(self):
        """完成执行，更新最终状态"""
        try:
            self.scene_history.end_time = timezone.now()
            self.scene_history.total_time = int(
                (self.scene_history.end_time - self.scene_history.start_time).total_seconds() * 1000
            )

            # 判断整体执行结果
            if self.scene_history.failed_steps > 0:
                self.scene_history.status = 'failed'
                self.scene_history.is_success = False
            else:
                self.scene_history.status = 'completed'
                self.scene_history.is_success = True

            self.scene_history.save()

            # 更新场景的最后执行结果
            self.scene.last_result = 'success' if self.scene_history.is_success else 'failed'
            self.scene.last_execution_time = self.scene_history.end_time
            self.scene.save()

        except Exception as e:
            SimpleLogger.exception(e)

    def _handle_execution_error(self, error_message: str):
        """处理执行错误"""
        try:
            if self.scene_history:
                self.scene_history.end_time = timezone.now()
                self.scene_history.total_time = int(
                    (self.scene_history.end_time - self.scene_history.start_time).total_seconds() * 1000
                )
                self.scene_history.status = 'failed'
                self.scene_history.is_success = False
                self.scene_history.error_message = error_message
                self.scene_history.save()

                # 更新场景的最后执行结果
                self.scene.last_result = 'failed'
                self.scene.last_execution_time = self.scene_history.end_time
                self.scene.save()

        except Exception as e:
            SimpleLogger.exception(e)

    def stop_execution(self):
        """停止执行"""
        try:
            if self.scene_history and self.scene_history.status == 'running':
                self.scene_history.status = 'stopped'
                self.scene_history.end_time = timezone.now()
                self.scene_history.total_time = int(
                    (self.scene_history.end_time - self.scene_history.start_time).total_seconds() * 1000
                )
                self.scene_history.save()

                # 更新场景的最后执行结果
                self.scene.last_result = 'stopped'
                self.scene.last_execution_time = self.scene_history.end_time
                self.scene.save()

        except Exception as e:
            SimpleLogger.exception(e)
