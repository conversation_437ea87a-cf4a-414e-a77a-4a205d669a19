# coding=utf-8
"""
Created on 2025-09-15

@author: zhangpeng
"""

from rest_framework import generics, status
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import AllowAny
from rest_framework.authentication import SessionAuthentication, BasicAuthentication
from django.shortcuts import get_object_or_404
from django.core.cache import cache
from django.db.models import Q, Max
from django.db import models
from django.utils import timezone

from teamvision.project.models import (
    ApiTestCollection, ApiTestCase, ApiTestEnvironment, ApiTestHistory,
    ApiTestScene, ApiTestSceneStep, ApiTestSceneHistory, ApiTestSceneStepRecord
)
from teamvision.api.project.serializer.api_test_case_serializer import (
    ApiTestCollectionSerializer, ApiTestCaseSerializer, ApiTestCollectionTestCaseTreeSerializer,
    ApiTestEnvironmentSerializer, ApiTestHistorySerializer,
    ApiTestCaseExecuteSerializer, ApiTestCodeSnippetSerializer,
    ApiTestSceneSerializer, ApiTestSceneListSerializer, ApiTestSceneStepSerializer,
    ApiTestSceneHistorySerializer, ApiTestSceneHistoryListSerializer,
    ApiTestSceneStepRecordSerializer, ApiTestSceneExecuteSerializer,
    ApiTestSceneStepOrderSerializer
)
from teamvision.api.project.views.CsrfExemptSessionAuthentication import CsrfExemptSessionAuthentication


# ==================== 集合管理视图 ====================

class ApiTestCollectionListView(generics.ListCreateAPIView):
    """
    GET /api/project/{project_id}/api-test/collections/
    POST /api/project/{project_id}/api-test/collections/
    """
    serializer_class = ApiTestCollectionSerializer
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)
    permission_classes = [AllowAny]

    def get_queryset(self):
        project_id = self.kwargs['project_id']
        parent = self.request.GET.get('parent', 0)
        return ApiTestCollection.objects.get_project_collections(project_id, parent)

    def perform_create(self, serializer):
        project_id = self.kwargs['project_id']
        serializer.save(project_id=project_id, creator=self.request.user.id)

    def post(self, request, *args, **kwargs):
        # 清除相关缓存
        project_id = self.kwargs['project_id']
        cache.delete(f'api_collections_{project_id}')
        return self.create(request, *args, **kwargs)


class ApiTestCollectionDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    GET/PUT/DELETE /api/project/{project_id}/api-test/collections/{collection_id}/
    """
    serializer_class = ApiTestCollectionSerializer
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)
    permission_classes = [AllowAny]

    def get_object(self):
        collection_id = self.kwargs['collection_id']
        return get_object_or_404(ApiTestCollection, id=collection_id, IsActive=True)

    def perform_destroy(self, instance):
        # 软删除
        instance.IsActive = False
        instance.save()
        # 清除缓存
        cache.delete(f'api_collections_{instance.project_id}')


class ApiTestCollectionTreeView(APIView):
    """
    GET /api/project/{project_id}/api-test/collections/tree/
    """
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)
    permission_classes = [AllowAny]

    def get(self, request, project_id):
        try:
            # 尝试从缓存获取
            cache_key = f'api_collections_tree_{project_id}'
            tree_data = cache.get(cache_key)

            if tree_data is None:
                tree_data = ApiTestCollection.objects.get_collection_tree(project_id)
                # 缓存30s
                cache.set(cache_key, tree_data, 30)

            return Response(tree_data)
        except Exception as e:
            return Response(None, status=500)


class ApiTestCollectionApiCaseTreeView(generics.ListAPIView):
    """
    GET /api/project/{project_id}/api-test/collections/tree_V2/
    """
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)
    serializer_class = ApiTestCollectionTestCaseTreeSerializer
    permission_classes = [AllowAny]

    def get_queryset(self):
        project_id = self.kwargs['project_id']
        return ApiTestCollection.objects.filter(project_id=project_id, parent=0, IsActive=True)


class ApiTestCollectionTreeLazyLoadView(APIView):
    """
    GET /api/project/{project_id}/api-test/collections/tree/lazyload/{collection_id}
    """
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)
    permission_classes = [AllowAny]

    def get(self, request, collection_id):
        try:
            # 尝试从缓存获取
            cache_key = f'api_collections_tree_lazyload_{collection_id}'
            tree_data = cache.get(cache_key)

            if tree_data is None:
                tree_data = ApiTestCollection.objects.get_collection_tree_lazyload(collection_id)
                # 缓存30s
                cache.set(cache_key, tree_data, 30)

            return Response(tree_data)
        except Exception as e:
            return Response(None, status=500)

        
# ==================== 测试用例管理视图 ====================

class ApiTestCaseListView(generics.ListCreateAPIView):
    """
    GET /api/project/{project_id}/api-test/cases/
    POST /api/project/{project_id}/api-test/cases/
    """
    serializer_class = ApiTestCaseSerializer
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)
    permission_classes = [AllowAny]

    def get_queryset(self):
        project_id = self.kwargs['project_id']
        collection_id = self.request.GET.get('collection_id')

        if collection_id:
            return ApiTestCase.objects.get_collection_cases(collection_id)
        else:
            return ApiTestCase.objects.get_project_cases(project_id)

    def perform_create(self, serializer):
        serializer.save(creator=self.request.user.id)


class ApiTestCaseDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    GET/PUT/DELETE /api/project/{project_id}/api-test/cases/{case_id}/
    """
    serializer_class = ApiTestCaseSerializer
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)
    permission_classes = [AllowAny]

    def get_object(self):
        case_id = self.kwargs['case_id']
        return get_object_or_404(ApiTestCase, id=case_id, IsActive=True)

    def perform_destroy(self, instance):
        # 软删除
        instance.IsActive = False
        instance.save()


class ApiTestCaseExecuteView(APIView):
    """
    POST /api/project/{project_id}/api-test/cases/{case_id}/execute/
    """
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)
    permission_classes = [AllowAny]

    def post(self, request, project_id, case_id):
        try:
            # 验证请求数据
            serializer = ApiTestCaseExecuteSerializer(data=request.data)
            if not serializer.is_valid():
                return Response(data=serializer.errors, status=400)

            test_case = get_object_or_404(ApiTestCase, id=case_id, IsActive=True)
            environment_id = serializer.validated_data.get('environment_id')
            override_config = serializer.validated_data.get('override_config', {})

            # 导入并使用执行器类
            from business.project.api_test_executor import ApiTestExecutor
            executor = ApiTestExecutor(test_case, environment_id, override_config)
            result = executor.execute()

            # 保存执行历史
            history = ApiTestHistory.objects.create(
                project_id=project_id,
                test_case=test_case,
                request_snapshot=executor.get_request_snapshot(),
                response_status=result.get('status_code'),
                response_headers=result.get('headers', {}),
                response_body=result.get('body', ''),
                response_time=result.get('response_time'),
                response_size=result.get('response_size'),
                test_results=result.get('test_results', []),
                is_success=result.get('is_success', True),
                error_message=result.get('error_message'),
                executor=request.user.id
            )

            if result.get('is_success', True):
                return Response(data={'history_id': history.id,'response': result},
                                status=status.HTTP_200_OK)
            else:
                return Response(data={'history_id': history.id,'response': result},
                                status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        except Exception as e:
            return Response(data={'result': None}, status=500)


# ==================== 环境管理视图 ====================

class ApiTestEnvironmentListView(generics.ListCreateAPIView):
    """
    GET /api/project/{project_id}/api-test/environments/
    POST /api/project/{project_id}/api-test/environments/
    """
    serializer_class = ApiTestEnvironmentSerializer
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)
    permission_classes = [AllowAny]

    def get_queryset(self):
        project_id = self.kwargs['project_id']
        return ApiTestEnvironment.objects.get_project_environments(project_id)

    def get_serializer_context(self):
        context = super().get_serializer_context()
        # 在列表视图中隐藏敏感变量的值
        context['hide_secrets'] = True
        return context

    def perform_create(self, serializer):
        project_id = self.kwargs['project_id']
        serializer.save(project_id=project_id, creator=self.request.user.id)


class ApiTestEnvironmentDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    GET/PUT/DELETE /api/project/{project_id}/api-test/environments/{env_id}/
    """
    serializer_class = ApiTestEnvironmentSerializer
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)
    permission_classes = [AllowAny]

    def get_object(self):
        env_id = self.kwargs['env_id']
        return get_object_or_404(ApiTestEnvironment, id=env_id, IsActive=True)

    def perform_destroy(self, instance):
        # 软删除
        instance.IsActive = False
        instance.save()


# ==================== 执行历史视图 ====================

class ApiTestHistoryListView(generics.ListAPIView):
    """
    GET /api/project/{project_id}/api-test/history/
    """
    serializer_class = ApiTestHistorySerializer
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)
    permission_classes = [AllowAny]

    def get_queryset(self):
        project_id = self.kwargs['project_id']
        case_id = self.request.GET.get('case_id')
        limit = int(self.request.GET.get('limit', 50))

        if case_id:
            return ApiTestHistory.objects.get_case_history(case_id, limit)
        else:
            return ApiTestHistory.objects.get_project_history(project_id, limit)


class ApiTestHistoryDetailView(generics.RetrieveAPIView):
    """
    GET /api/project/{project_id}/api-test/history/{history_id}/
    """
    serializer_class = ApiTestHistorySerializer
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)
    permission_classes = [AllowAny]

    def get_object(self):
        history_id = self.kwargs['history_id']
        return get_object_or_404(ApiTestHistory, id=history_id, IsActive=True)


# ==================== 代码片段生成视图 ====================

class ApiTestCodeSnippetView(APIView):
    """
    POST /api/project/{project_id}/api-test/cases/{case_id}/code-snippet/
    """
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)
    permission_classes = [AllowAny]

    def post(self, request, project_id, case_id):
        try:
            # 验证请求数据
            serializer = ApiTestCodeSnippetSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({
                    'code': 1002,
                    'msg': '请求参数错误',
                    'result': serializer.errors
                }, status=400)

            test_case = get_object_or_404(ApiTestCase, id=case_id, IsActive=True)
            language = serializer.validated_data.get('language', 'curl')
            environment_id = serializer.validated_data.get('environment_id')

            # 获取环境变量
            environment = None
            if environment_id:
                environment = get_object_or_404(ApiTestEnvironment, id=environment_id, IsActive=True)

            # 导入并使用代码生成器类
            from business.project.code_snippet_generator import CodeSnippetGenerator
            generator = CodeSnippetGenerator(test_case, environment)
            code_snippet = generator.generate(language)

            return Response({
                'code': 0,
                'msg': 'success',
                'result': {
                    'language': language,
                    'code_snippet': code_snippet
                }
            })

        except Exception as e:
            return Response({
                'code': 1001,
                'msg': str(e),
                'result': None
            }, status=500)


# ==================== 场景管理视图 ====================

class ApiTestSceneListView(generics.ListCreateAPIView):
    """
    GET /api/project/{project_id}/api-test/scenes/
    POST /api/project/{project_id}/api-test/scenes/
    """
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)
    permission_classes = [AllowAny]

    def get_serializer_class(self):
        if self.request.method == 'GET':
            return ApiTestSceneListSerializer
        return ApiTestSceneSerializer

    def get_queryset(self):
        project_id = self.kwargs['project_id']
        level = self.request.GET.get('level')
        status = self.request.GET.get('status')
        search = self.request.GET.get('search')

        queryset = ApiTestScene.objects.get_project_scenes(project_id)

        if level:
            queryset = queryset.filter(level=level)
        if status:
            queryset = queryset.filter(status=status)
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) | Q(description__icontains=search)
            )

        return queryset

    def perform_create(self, serializer):
        project_id = self.kwargs['project_id']
        serializer.save(project_id=project_id, creator=self.request.user.id)


class ApiTestSceneDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    GET/PUT/DELETE /api/project/{project_id}/api-test/scenes/{scene_id}/
    """
    serializer_class = ApiTestSceneSerializer
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)
    permission_classes = [AllowAny]

    def get_object(self):
        scene_id = self.kwargs['scene_id']
        return get_object_or_404(ApiTestScene, id=scene_id, IsActive=True)

    def perform_destroy(self, instance):
        # 软删除
        instance.IsActive = False
        instance.save()


class ApiTestSceneCopyView(APIView):
    """
    POST /api/project/{project_id}/api-test/scenes/{scene_id}/copy/
    """
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)
    permission_classes = [AllowAny]

    def post(self, request, project_id, scene_id):
        try:
            original_scene = get_object_or_404(ApiTestScene, id=scene_id, IsActive=True)

            # 复制场景
            new_scene = ApiTestScene.objects.create(
                project_id=project_id,
                name=f"{original_scene.name}_copy",
                description=original_scene.description,
                module=original_scene.module,
                level=original_scene.level,
                status='progress',  # 复制的场景状态重置为进行中
                tags=original_scene.tags,
                global_variables=original_scene.global_variables,
                global_headers=original_scene.global_headers,
                pre_script=original_scene.pre_script,
                post_script=original_scene.post_script,
                assertions=original_scene.assertions,
                settings=original_scene.settings,
                is_parallel=original_scene.is_parallel,
                creator=request.user.id
            )

            # 复制步骤
            original_steps = ApiTestSceneStep.objects.filter(
                scene=original_scene, IsActive=True
            ).order_by('step_order')

            for step in original_steps:
                ApiTestSceneStep.objects.create(
                    scene=new_scene,
                    step_name=step.step_name,
                    description=step.description,
                    step_type=step.step_type,
                    step_order=step.step_order,
                    is_enabled=step.is_enabled,
                    method=step.method,
                    url=step.url,
                    headers=step.headers,
                    query_params=step.query_params,
                    body_type=step.body_type,
                    body_data=step.body_data,
                    wait_time=step.wait_time,
                    condition=step.condition,
                    operator=step.operator,
                    expected_value=step.expected_value,
                    test_case=step.test_case,
                    variable_extracts=step.variable_extracts,
                    step_assertions=step.step_assertions,
                    creator=request.user.id
                )

            serializer = ApiTestSceneSerializer(new_scene)
            return Response({
                'code': 0,
                'msg': 'success',
                'result': serializer.data
            })

        except Exception as e:
            return Response({
                'code': 1001,
                'msg': str(e),
                'result': None
            }, status=500)


# ==================== 场景步骤管理视图 ====================

class ApiTestSceneStepListView(generics.ListCreateAPIView):
    """
    GET /api/project/{project_id}/api-test/scenes/{scene_id}/steps/
    POST /api/project/{project_id}/api-test/scenes/{scene_id}/steps/
    """
    serializer_class = ApiTestSceneStepSerializer
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)
    permission_classes = [AllowAny]

    def get_queryset(self):
        scene_id = self.kwargs['scene_id']
        return ApiTestSceneStep.objects.get_scene_steps(scene_id)

    def perform_create(self, serializer):
        scene_id = self.kwargs['scene_id']
        scene = get_object_or_404(ApiTestScene, id=scene_id, IsActive=True)

        # 自动设置步骤顺序
        max_order = ApiTestSceneStep.objects.filter(
            scene=scene, IsActive=True
        ).aggregate(max_order=Max('step_order'))['max_order'] or 0

        serializer.save(scene=scene, step_order=max_order + 1, creator=self.request.user.id)


class ApiTestSceneStepDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    GET/PUT/DELETE /api/project/{project_id}/api-test/scenes/{scene_id}/steps/{step_id}/
    """
    serializer_class = ApiTestSceneStepSerializer
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)
    permission_classes = [AllowAny]

    def get_object(self):
        step_id = self.kwargs['step_id']
        scene_id = self.kwargs['scene_id']
        return get_object_or_404(
            ApiTestSceneStep,
            id=step_id,
            scene_id=scene_id,
            IsActive=True
        )

    def perform_destroy(self, instance):
        # 软删除
        instance.IsActive = False
        instance.save()


class ApiTestSceneStepOrderView(APIView):
    """
    PUT /api/project/{project_id}/api-test/scenes/{scene_id}/steps/order/
    """
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)
    permission_classes = [AllowAny]

    def put(self, request, project_id, scene_id):
        try:
            serializer = ApiTestSceneStepOrderSerializer(data=request.data)
            if not serializer.is_valid():
                return Response(data=serializer.errors, status=400)

            step_orders = serializer.validated_data['step_orders']

            # 验证步骤是否属于指定场景
            step_ids = list(step_orders.keys())
            valid_steps = ApiTestSceneStep.objects.filter(
                id__in=step_ids, scene_id=scene_id, IsActive=True
            ).values_list('id', flat=True)

            invalid_steps = set(map(int, step_ids)) - set(valid_steps)
            if invalid_steps:
                return Response({
                    'code': 1002,
                    'msg': f'步骤不属于指定场景: {invalid_steps}',
                    'result': None
                }, status=400)

            # 批量更新步骤顺序
            success = ApiTestSceneStep.objects.update_steps_order(step_orders)

            if success:
                return Response({
                    'code': 0,
                    'msg': 'success',
                    'result': {'updated_count': len(step_orders)}
                })
            else:
                return Response({
                    'code': 1001,
                    'msg': '更新步骤顺序失败',
                    'result': None
                }, status=500)

        except Exception as e:
            return Response({
                'code': 1001,
                'msg': str(e),
                'result': None
            }, status=500)


# ==================== 场景执行视图 ====================

class ApiTestSceneExecuteView(APIView):
    """
    POST /api/project/{project_id}/api-test/scenes/{scene_id}/execute/
    """
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)
    permission_classes = [AllowAny]

    def post(self, request, project_id, scene_id):
        try:
            # 验证请求数据
            serializer = ApiTestSceneExecuteSerializer(data=request.data)
            if not serializer.is_valid():
                return Response(data=serializer.errors, status=400)

            scene = get_object_or_404(ApiTestScene, id=scene_id, IsActive=True)
            environment_id = serializer.validated_data.get('environment_id')

            # 创建执行器并执行场景
            from business.project.api_test_scene_executor import ApiTestSceneExecutor
            executor = ApiTestSceneExecutor(scene, environment_id)
            result = executor.execute(request.user.id)

            return Response({
                'code': 0,
                'msg': 'success',
                'result': result
            })

        except Exception as e:
            return Response({
                'code': 1001,
                'msg': str(e),
                'result': None
            }, status=500)


class ApiTestSceneStopView(APIView):
    """
    POST /api/project/{project_id}/api-test/scenes/{scene_id}/stop/
    """
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)
    permission_classes = [AllowAny]

    def post(self, request, project_id, scene_id):
        try:
            history_id = request.data.get('history_id')
            if not history_id:
                return Response({
                    'code': 1002,
                    'msg': '缺少执行历史ID',
                    'result': None
                }, status=400)

            # 查找执行历史
            history = get_object_or_404(
                ApiTestSceneHistory,
                id=history_id,
                scene_id=scene_id,
                IsActive=True
            )

            if history.status != 'running':
                return Response({
                    'code': 1003,
                    'msg': '场景未在执行中',
                    'result': None
                }, status=400)

            # 停止执行
            history.status = 'stopped'
            history.end_time = timezone.now()
            history.total_time = int(
                (history.end_time - history.start_time).total_seconds() * 1000
            )
            history.save()

            return Response({
                'code': 0,
                'msg': 'success',
                'result': {
                    'history_id': history.id,
                    'status': history.status
                }
            })

        except Exception as e:
            return Response({
                'code': 1001,
                'msg': str(e),
                'result': None
            }, status=500)


# ==================== 场景执行历史视图 ====================

class ApiTestSceneHistoryListView(generics.ListAPIView):
    """
    GET /api/project/{project_id}/api-test/scenes/history/
    """
    serializer_class = ApiTestSceneHistoryListSerializer
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)
    permission_classes = [AllowAny]

    def get_queryset(self):
        project_id = self.kwargs['project_id']
        scene_id = self.request.GET.get('scene_id')

        if scene_id:
            return ApiTestSceneHistory.objects.get_scene_history(scene_id)
        else:
            return ApiTestSceneHistory.objects.get_project_history(project_id)


class ApiTestSceneHistoryDetailView(generics.RetrieveAPIView):
    """
    GET /api/project/{project_id}/api-test/scenes/history/{history_id}/
    """
    serializer_class = ApiTestSceneHistorySerializer
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)
    permission_classes = [AllowAny]

    def get_object(self):
        history_id = self.kwargs['history_id']
        project_id = self.kwargs['project_id']
        return get_object_or_404(
            ApiTestSceneHistory,
            id=history_id,
            project_id=project_id,
            IsActive=True
        )


class ApiTestSceneStepRecordListView(generics.ListAPIView):
    """
    GET /api/project/{project_id}/api-test/scenes/history/{history_id}/steps/
    """
    serializer_class = ApiTestSceneStepRecordSerializer
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)
    permission_classes = [AllowAny]

    def get_queryset(self):
        history_id = self.kwargs['history_id']
        return ApiTestSceneStepRecord.objects.get_history_records(history_id)
