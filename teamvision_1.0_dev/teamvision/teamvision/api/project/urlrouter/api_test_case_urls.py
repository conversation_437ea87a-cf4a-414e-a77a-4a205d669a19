# coding=utf-8
"""
Created on 2025-09-15

@author: zhangpeng
"""

from django.urls import re_path
from teamvision.api.project.views import api_test_case_view

api_test_case_router = [
    # 集合管理
    re_path(r"(?P<project_id>\d+)/api-test/collections/$", api_test_case_view.ApiTestCollectionListView.as_view()),
    re_path(r"(?P<project_id>\d+)/api-test/collections/(?P<collection_id>\d+)/$", api_test_case_view.ApiTestCollectionDetailView.as_view()),
    re_path(r"(?P<project_id>\d+)/api-test/collections/tree/$", api_test_case_view.ApiTestCollectionTreeView.as_view()),
    re_path(r"(?P<project_id>\d+)/api-test/collections/tree_V2/$", api_test_case_view.ApiTestCollectionApiCaseTreeView.as_view()),
    re_path(r"(?P<project_id>\d+)/api-test/collections/tree/lazyload/(?P<collection_id>\d+)$", api_test_case_view.ApiTestCollectionTreeLazyLoadView.as_view()),

    # 测试用例管理
    re_path(r"(?P<project_id>\d+)/api-test/cases/$", api_test_case_view.ApiTestCaseListView.as_view()),
    re_path(r"(?P<project_id>\d+)/api-test/cases/(?P<case_id>\d+)/$", api_test_case_view.ApiTestCaseDetailView.as_view()),

    # 用例执行
    re_path(r"(?P<project_id>\d+)/api-test/cases/(?P<case_id>\d+)/execute/$", api_test_case_view.ApiTestCaseExecuteView.as_view()),

    # 环境管理
    re_path(r"(?P<project_id>\d+)/api-test/environments/$", api_test_case_view.ApiTestEnvironmentListView.as_view()),
    re_path(r"(?P<project_id>\d+)/api-test/environments/(?P<env_id>\d+)/$", api_test_case_view.ApiTestEnvironmentDetailView.as_view()),

    # 执行历史
    re_path(r"(?P<project_id>\d+)/api-test/history/$", api_test_case_view.ApiTestHistoryListView.as_view()),
    re_path(r"(?P<project_id>\d+)/api-test/history/(?P<history_id>\d+)/$", api_test_case_view.ApiTestHistoryDetailView.as_view()),

    # 代码生成
    re_path(r"(?P<project_id>\d+)/api-test/cases/(?P<case_id>\d+)/code-snippet/$", api_test_case_view.ApiTestCodeSnippetView.as_view()),

    # ==================== 场景管理 ====================
    # 场景CRUD
    re_path(r"(?P<project_id>\d+)/api-test/scenes/$", api_test_case_view.ApiTestSceneListView.as_view()),
    re_path(r"(?P<project_id>\d+)/api-test/scenes/(?P<scene_id>\d+)/$", api_test_case_view.ApiTestSceneDetailView.as_view()),
    re_path(r"(?P<project_id>\d+)/api-test/scenes/(?P<scene_id>\d+)/copy/$", api_test_case_view.ApiTestSceneCopyView.as_view()),

    # 场景步骤管理
    re_path(r"(?P<project_id>\d+)/api-test/scenes/(?P<scene_id>\d+)/steps/$", api_test_case_view.ApiTestSceneStepListView.as_view()),
    re_path(r"(?P<project_id>\d+)/api-test/scenes/(?P<scene_id>\d+)/steps/(?P<step_id>\d+)/$", api_test_case_view.ApiTestSceneStepDetailView.as_view()),
    re_path(r"(?P<project_id>\d+)/api-test/scenes/(?P<scene_id>\d+)/steps/order/$", api_test_case_view.ApiTestSceneStepOrderView.as_view()),

    # 场景执行
    re_path(r"(?P<project_id>\d+)/api-test/scenes/(?P<scene_id>\d+)/execute/$", api_test_case_view.ApiTestSceneExecuteView.as_view()),
    re_path(r"(?P<project_id>\d+)/api-test/scenes/(?P<scene_id>\d+)/stop/$", api_test_case_view.ApiTestSceneStopView.as_view()),

    # 场景执行历史
    re_path(r"(?P<project_id>\d+)/api-test/scenes/history/$", api_test_case_view.ApiTestSceneHistoryListView.as_view()),
    re_path(r"(?P<project_id>\d+)/api-test/scenes/history/(?P<history_id>\d+)/$", api_test_case_view.ApiTestSceneHistoryDetailView.as_view()),
    re_path(r"(?P<project_id>\d+)/api-test/scenes/history/(?P<history_id>\d+)/steps/$", api_test_case_view.ApiTestSceneStepRecordListView.as_view()),
]

