# 场景步骤管理器交互方式修改总结

## 修改目标
将 SceneStepsManager.vue 中的"添加步骤"交互方式从对话框改为下拉菜单+抽屉的方式。

## 修改内容

### 1. 模板层修改
- **移除组件**：删除了 `el-dialog` 添加步骤对话框和编辑步骤对话框
- **新增组件**：添加了 `Drawer` 组件用于步骤编辑
- **保留功能**：保持了原有的下拉菜单功能

### 2. 抽屉配置
```vue
<Drawer 
  :title="drawerTitle" 
  v-model="showStepDrawer" 
  :width="50" 
  :mask="false" 
  :transfer="false" 
  :inner="true"
  @on-close="resetStepForm">
```

### 3. 数据结构重构
**原有数据属性：**
- `showAddDialog: false`
- `showEditDialog: false`
- `addStepForm: {}`
- `editStepForm: {}`
- `addStepRules: {}`

**新的数据属性：**
- `showStepDrawer: false`
- `isEditMode: false`
- `stepForm: {}`
- `stepFormRules: {}`

### 4. 计算属性
新增 `drawerTitle` 计算属性，根据 `isEditMode` 动态显示标题：
- 添加模式：显示"添加步骤"
- 编辑模式：显示"编辑步骤"

### 5. 方法重构

#### 统一的保存方法
```javascript
saveStep() {
  this.$refs.stepForm.validate((valid) => {
    if (valid) {
      if (this.isEditMode) {
        // 更新现有步骤
        this.$set(this.localSteps, this.editingIndex, { ...this.stepForm })
        this.$message.success('步骤更新成功')
      } else {
        // 添加新步骤
        const newStep = {
          ...this.stepForm,
          id: Date.now(),
          step_order: this.localSteps.length + 1
        }
        this.localSteps.push(newStep)
        this.$message.success('步骤添加成功')
      }
      this.showStepDrawer = false
      this.resetStepForm()
    }
  })
}
```

#### 编辑步骤方法
```javascript
editStep(step, index) {
  this.isEditMode = true
  this.editingIndex = index
  this.stepForm = { ...step }
  this.showStepDrawer = true
}
```

#### 处理添加步骤命令
```javascript
handleAddStepCommand(command) {
  const stepTypeMap = {
    'import_request': 'api',
    'custom_request': 'api',
    'loop_controller': 'controller',
    'condition_controller': 'controller',
    'if_controller': 'controller',
    'script_step': 'script',
    'wait_step': 'wait'
  };

  const stepType = stepTypeMap[command];
  if (stepType) {
    this.initStepForm(stepType, command);
    this.isEditMode = false;
    this.showStepDrawer = true;
  }
}
```

#### 统一的重置方法
```javascript
resetStepForm() {
  this.stepForm = {
    step_type: '',
    step_name: '',
    method: 'GET',
    url: '',
    wait_time: 1000,
    condition: '',
    operator: 'equals',
    expected_value: '',
    description: '',
    is_enabled: true
  }
  this.isEditMode = false
  this.editingIndex = -1
  if (this.$refs.stepForm) {
    this.$refs.stepForm.resetFields()
  }
}
```

### 6. 样式修改
新增抽屉底部按钮样式：
```css
.drawer-footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 16px 24px;
  border-top: 1px solid #e8e8e8;
  background: #fff;
  text-align: right;
}
```

## 交互流程

### 添加步骤流程
1. 用户点击"添加步骤"按钮
2. 显示下拉菜单，包含各种步骤类型选项
3. 用户选择步骤类型（如"自定义请求"、"等待时间"等）
4. 右侧弹出抽屉，显示对应的步骤编辑表单
5. 用户填写步骤信息，点击"添加"按钮保存
6. 抽屉关闭，新步骤添加到列表中

### 编辑步骤流程
1. 用户点击现有步骤的"编辑"按钮
2. 右侧弹出抽屉，显示步骤编辑表单
3. 表单预填充当前步骤的信息
4. 步骤类型字段被禁用（不允许修改）
5. 用户修改步骤信息，点击"更新"按钮保存
6. 抽屉关闭，步骤信息更新

## 优势
1. **更好的空间利用**：抽屉不会遮挡主界面，用户可以同时查看步骤列表
2. **统一的交互体验**：添加和编辑使用相同的界面，减少学习成本
3. **更流畅的操作**：从下拉菜单直接进入编辑状态，减少点击次数
4. **更好的响应式支持**：抽屉在不同屏幕尺寸下都有良好的表现

## 兼容性
- 保持了所有原有的功能特性
- 保持了原有的数据结构和事件触发
- 保持了原有的验证规则和错误处理
- 保持了原有的步骤类型支持

## 文件位置
`teamvision_1.0_dev/teamvision_fontend/src/pages/project/project-testing/api-testcase copy/components/scene/SceneStepsManager.vue`
