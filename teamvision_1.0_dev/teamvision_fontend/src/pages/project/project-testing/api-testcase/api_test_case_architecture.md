# API 测试用例功能开发设计文档

## 目录

1. [项目概述](#1-项目概述)
2. [数据模型设计](#2-数据模型设计)
3. [API 接口设计](#3-api-接口设计)
4. [核心业务逻辑](#4-核心业务逻辑)
5. [部署和配置](#5-部署和配置)
6. [API 测试场景功能特性](#6-api-测试场景功能特性)
7. [高级功能实现](#7-高级功能实现)
8. [后端实现要点总结](#8-后端实现要点总结)

---

## 1. 项目概述

### 1.1 功能描述

基于前端已完成的 API 测试用例功能，设计并实现后端 API 接口，支持类似 Postman 的 API 测试功能，包括：

- API 请求集合管理
- 环境变量管理
- 请求历史记录
- 请求发送与响应处理
- 代码片段生成
- 测试场景

### 1.2 技术栈

- **后端框架**: Django 4.1 + Django REST Framework 3.14.0
- **数据库**: MySQL (通过现有项目配置)
- **认证**: Session Authentication + Basic Authentication
- **API 文档**: drf-yasg (Swagger)

## 2. 数据模型设计

### 2.1 核心模型

#### 2.1.1 API 测试集合 (ApiTestCollection)

```python
class ApiTestCollection(ProjectModel):
    """API测试集合"""
    project_id = models.IntegerField(verbose_name='项目ID')
    name = models.CharField(max_length=200, verbose_name='集合名称')
    description = models.TextField(null=True, blank=True, verbose_name='描述')
    parent = models.IntegerField(default=0, verbose_name='父级集合ID')
    creator = models.IntegerField(verbose_name='创建人')
    is_folder = models.BooleanField(default=False, verbose_name='是否为文件夹')
    sort_order = models.IntegerField(default=0, verbose_name='排序')

    class Meta:
        app_label = "project"
        db_table = "project_api_test_collection"
```

#### 2.1.2 API 测试用例 (ApiTestCase)

```python
class ApiTestCase(ProjectModel):
    """API测试用例"""
    collection = models.ForeignKey(ApiTestCollection, on_delete=models.CASCADE, verbose_name='所属集合')
    name = models.CharField(max_length=200, verbose_name='用例名称')
    description = models.TextField(null=True, blank=True, verbose_name='描述')

    # HTTP请求信息
    method = models.CharField(max_length=10, default='GET', verbose_name='HTTP方法')
    url = models.TextField(verbose_name='请求URL')

    # 请求配置
    headers = models.JSONField(default=dict, verbose_name='请求头')
    query_params = models.JSONField(default=dict, verbose_name='查询参数')
    path_variables = models.JSONField(default=dict, verbose_name='路径变量')

    # 请求体配置
    body_type = models.CharField(max_length=50, default='none', verbose_name='请求体类型')
    body_data = models.JSONField(default=dict, verbose_name='请求体数据')

    # 认证配置
    auth_type = models.CharField(max_length=50, default='none', verbose_name='认证类型')
    auth_config = models.JSONField(default=dict, verbose_name='认证配置')

    # 脚本配置
    pre_request_script = models.TextField(null=True, blank=True, verbose_name='前置脚本')
    post_request_script = models.TextField(null=True, blank=True, verbose_name='后置脚本')

    # 测试断言
    test_assertions = models.JSONField(default=list, verbose_name='测试断言')

    creator = models.IntegerField(verbose_name='创建人')
    sort_order = models.IntegerField(default=0, verbose_name='排序')

    class Meta:
        app_label = "project"
        db_table = "project_api_test_case"
```

#### 2.1.3 环境变量 (ApiTestEnvironment)

```python
class ApiTestEnvironment(ProjectModel):
    """API测试环境变量"""
    project_id = models.IntegerField(verbose_name='项目ID')
    name = models.CharField(max_length=100, verbose_name='环境名称')
    description = models.TextField(null=True, blank=True, verbose_name='描述')
    variables = models.JSONField(default=dict, verbose_name='环境变量')
    secrets = models.JSONField(default=dict, verbose_name='敏感变量')
    is_global = models.BooleanField(default=False, verbose_name='是否为全局环境')
    creator = models.IntegerField(verbose_name='创建人')

    class Meta:
        app_label = "project"
        db_table = "project_api_test_environment"
```

#### 2.1.4 执行历史 (ApiTestHistory)

```python
class ApiTestHistory(ProjectModel):
    """API测试执行历史"""
    project_id = models.IntegerField(verbose_name='项目ID')
    test_case = models.ForeignKey(ApiTestCase, null=True, on_delete=models.SET_NULL, verbose_name='关联测试用例')

    # 请求信息快照
    request_snapshot = models.JSONField(verbose_name='请求快照')

    # 响应信息
    response_status = models.IntegerField(null=True, verbose_name='响应状态码')
    response_headers = models.JSONField(default=dict, verbose_name='响应头')
    response_body = models.TextField(null=True, verbose_name='响应体')
    response_time = models.IntegerField(null=True, verbose_name='响应时间(ms)')
    response_size = models.IntegerField(null=True, verbose_name='响应大小(bytes)')

    # 测试结果
    test_results = models.JSONField(default=list, verbose_name='测试结果')
    is_success = models.BooleanField(default=True, verbose_name='是否成功')
    error_message = models.TextField(null=True, blank=True, verbose_name='错误信息')

    executor = models.IntegerField(verbose_name='执行人')
    executed_at = models.DateTimeField(auto_now_add=True, verbose_name='执行时间')

    class Meta:
        app_label = "project"
        db_table = "project_api_test_history"
```

#### 2.1.5 测试场景 (ApiTestScene)

```python
class ApiTestScene(ProjectModel):
    """API测试场景"""
    project_id = models.IntegerField(verbose_name='项目ID')
    name = models.CharField(max_length=200, verbose_name='场景名称')
    description = models.TextField(null=True, blank=True, verbose_name='场景描述')

    # 场景分类和状态
    module = models.CharField(max_length=100, null=True, blank=True, verbose_name='所属模块')
    level = models.CharField(max_length=10, default='P0', verbose_name='场景等级')  # P0, P1, P2
    status = models.CharField(max_length=20, default='progress', verbose_name='场景状态')  # progress, completed, pending
    tags = models.CharField(max_length=500, null=True, blank=True, verbose_name='标签')

    # 场景配置
    environment = models.ForeignKey(ApiTestEnvironment, null=True, on_delete=models.SET_NULL, verbose_name='执行环境')

    # 全局变量和配置
    global_variables = models.JSONField(default=dict, verbose_name='全局变量')
    global_headers = models.JSONField(default=dict, verbose_name='全局请求头')

    # 前置和后置脚本
    pre_script = models.TextField(null=True, blank=True, verbose_name='前置脚本')
    post_script = models.TextField(null=True, blank=True, verbose_name='后置脚本')

    # 断言配置
    assertions = models.JSONField(default=list, verbose_name='场景级断言')

    # 执行设置
    settings = models.JSONField(default=dict, verbose_name='执行设置')
    # settings 结构示例:
    # {
    #   "execution_mode": "sequential",  # sequential, parallel
    #   "continue_on_failure": true,
    #   "timeout": 30000,
    #   "retry_count": 0,
    #   "retry_interval": 1000,
    #   "notify_on_completion": false,
    #   "notify_on_failure": true
    # }

    creator = models.IntegerField(verbose_name='创建人')
    sort_order = models.IntegerField(default=0, verbose_name='排序')

    class Meta:
        app_label = "project"
        db_table = "project_api_test_scene"
```

#### 2.1.6 测试场景步骤 (ApiTestSceneStep)

```python
class ApiTestSceneStep(ProjectModel):
    """API测试场景步骤"""
    scene = models.ForeignKey(ApiTestScene, on_delete=models.CASCADE, verbose_name='所属场景')

    # 步骤基本信息
    step_name = models.CharField(max_length=200, verbose_name='步骤名称')
    step_type = models.CharField(max_length=20, verbose_name='步骤类型')  # api, wait, controller, case
    step_order = models.IntegerField(verbose_name='执行顺序')
    is_enabled = models.BooleanField(default=True, verbose_name='是否启用')
    description = models.TextField(null=True, blank=True, verbose_name='步骤描述')

    # API步骤配置 (当step_type='api'时使用)
    method = models.CharField(max_length=10, null=True, blank=True, verbose_name='HTTP方法')
    url = models.TextField(null=True, blank=True, verbose_name='请求URL')
    headers = models.JSONField(default=dict, verbose_name='请求头')
    query_params = models.JSONField(default=dict, verbose_name='查询参数')
    body_data = models.JSONField(default=dict, verbose_name='请求体数据')

    # 等待步骤配置 (当step_type='wait'时使用)
    wait_time = models.IntegerField(default=0, verbose_name='等待时间(ms)')

    # 控制器步骤配置 (当step_type='controller'时使用)
    condition = models.CharField(max_length=500, null=True, blank=True, verbose_name='条件表达式')
    operator = models.CharField(max_length=20, null=True, blank=True, verbose_name='操作符')  # equals, not_equals, greater_than, less_than, contains
    expected_value = models.CharField(max_length=500, null=True, blank=True, verbose_name='期望值')

    # 测试用例步骤配置 (当step_type='case'时使用)
    test_case = models.ForeignKey(ApiTestCase, null=True, blank=True, on_delete=models.SET_NULL, verbose_name='关联测试用例')

    # 参数覆盖
    override_headers = models.JSONField(default=dict, verbose_name='覆盖请求头')
    override_params = models.JSONField(default=dict, verbose_name='覆盖查询参数')
    override_body = models.JSONField(default=dict, verbose_name='覆盖请求体')

    # 数据提取和传递
    extract_variables = models.JSONField(default=list, verbose_name='提取变量配置')
    # 格式: [{"name": "user_id", "source": "response.body.data.id", "type": "jsonpath"}]

    # 前置和后置处理
    pre_script = models.TextField(null=True, blank=True, verbose_name='前置脚本')
    post_script = models.TextField(null=True, blank=True, verbose_name='后置脚本')

    # 等待和重试配置
    wait_before = models.IntegerField(default=0, verbose_name='执行前等待时间(ms)')
    wait_after = models.IntegerField(default=0, verbose_name='执行后等待时间(ms)')
    retry_count = models.IntegerField(default=0, verbose_name='重试次数')
    retry_interval = models.IntegerField(default=1000, verbose_name='重试间隔(ms)')

    class Meta:
        app_label = "project"
        db_table = "project_api_test_scene_step"
        ordering = ['step_order']
```

#### 2.1.7 场景执行历史 (ApiTestSceneHistory)

```python
class ApiTestSceneHistory(ProjectModel):
    """API测试场景执行历史"""
    project_id = models.IntegerField(verbose_name='项目ID')
    scene = models.ForeignKey(ApiTestScene, null=True, on_delete=models.SET_NULL, verbose_name='关联场景')

    # 执行配置快照
    scene_snapshot = models.JSONField(verbose_name='场景配置快照')

    # 执行结果统计
    total_steps = models.IntegerField(verbose_name='总步骤数')
    success_steps = models.IntegerField(verbose_name='成功步骤数')
    failed_steps = models.IntegerField(verbose_name='失败步骤数')
    skipped_steps = models.IntegerField(verbose_name='跳过步骤数')

    # 执行时间统计
    start_time = models.DateTimeField(verbose_name='开始时间')
    end_time = models.DateTimeField(null=True, verbose_name='结束时间')
    total_duration = models.IntegerField(null=True, verbose_name='总耗时(ms)')

    # 执行状态
    status = models.CharField(max_length=20, default='running', verbose_name='执行状态')
    # 状态: running, completed, failed, cancelled

    is_success = models.BooleanField(default=True, verbose_name='是否成功')
    error_message = models.TextField(null=True, blank=True, verbose_name='错误信息')

    executor = models.IntegerField(verbose_name='执行人')

    class Meta:
        app_label = "project"
        db_table = "project_api_test_scene_history"
```

#### 2.1.8 场景步骤执行记录 (ApiTestSceneStepRecord)

```python
class ApiTestSceneStepRecord(ProjectModel):
    """场景步骤执行记录"""
    scene_history = models.ForeignKey(ApiTestSceneHistory, on_delete=models.CASCADE, verbose_name='场景执行历史')
    step = models.ForeignKey(ApiTestSceneStep, null=True, on_delete=models.SET_NULL, verbose_name='关联步骤')

    # 步骤信息
    step_name = models.CharField(max_length=200, verbose_name='步骤名称')
    step_order = models.IntegerField(verbose_name='执行顺序')

    # 执行结果
    status = models.CharField(max_length=20, verbose_name='执行状态')
    # 状态: pending, running, success, failed, skipped

    # 请求响应信息
    request_snapshot = models.JSONField(null=True, verbose_name='请求快照')
    response_status = models.IntegerField(null=True, verbose_name='响应状态码')
    response_headers = models.JSONField(default=dict, verbose_name='响应头')
    response_body = models.TextField(null=True, verbose_name='响应体')
    response_time = models.IntegerField(null=True, verbose_name='响应时间(ms)')

    # 变量提取结果
    extracted_variables = models.JSONField(default=dict, verbose_name='提取的变量')

    # 断言结果
    assertion_results = models.JSONField(default=list, verbose_name='断言结果')

    # 错误信息
    error_message = models.TextField(null=True, blank=True, verbose_name='错误信息')

    # 执行时间
    start_time = models.DateTimeField(verbose_name='开始时间')
    end_time = models.DateTimeField(null=True, verbose_name='结束时间')
    duration = models.IntegerField(null=True, verbose_name='执行耗时(ms)')

    class Meta:
        app_label = "project"
        db_table = "project_api_test_scene_step_record"
        ordering = ['step_order']
```

### 2.2 模型管理器

#### 2.2.1 ApiTestCollectionManager

```python
class ApiTestCollectionManager(models.Manager):
    def get_project_collections(self, project_id, parent=0):
        """获取项目的集合树"""
        return self.filter(project_id=project_id, parent=parent, IsActive=True).order_by('sort_order', 'id')

    def get_collection_tree(self, project_id):
        """获取完整的集合树结构"""
        # 递归构建树结构的逻辑
        pass
```

#### 2.2.2 ApiTestCaseManager

```python
class ApiTestCaseManager(models.Manager):
    def get_collection_cases(self, collection_id):
        """获取集合下的测试用例"""
        return self.filter(collection_id=collection_id, IsActive=True).order_by('sort_order', 'id')
```

## 3. API 接口设计

### 3.1 URL 路由配置

#### 3.1.1 主路由 (api_test_case_urls.py)

```python
from django.urls import re_path
from teamvision.api.project.views import api_test_case_view

api_test_case_router = [
    # 集合管理
    re_path(r"(?P<project_id>\d+)/api-test/collections/$",
            api_test_case_view.ApiTestCollectionListView.as_view()),
    re_path(r"(?P<project_id>\d+)/api-test/collections/(?P<collection_id>\d+)/$",
            api_test_case_view.ApiTestCollectionDetailView.as_view()),
    re_path(r"(?P<project_id>\d+)/api-test/collections/tree/$",
            api_test_case_view.ApiTestCollectionTreeView.as_view()),

    # 测试用例管理
    re_path(r"(?P<project_id>\d+)/api-test/cases/$",
            api_test_case_view.ApiTestCaseListView.as_view()),
    re_path(r"(?P<project_id>\d+)/api-test/cases/(?P<case_id>\d+)/$",
            api_test_case_view.ApiTestCaseDetailView.as_view()),

    # 用例执行
    re_path(r"(?P<project_id>\d+)/api-test/cases/(?P<case_id>\d+)/execute/$",
            api_test_case_view.ApiTestCaseExecuteView.as_view()),

    # 环境管理
    re_path(r"(?P<project_id>\d+)/api-test/environments/$",
            api_test_case_view.ApiTestEnvironmentListView.as_view()),
    re_path(r"(?P<project_id>\d+)/api-test/environments/(?P<env_id>\d+)/$",
            api_test_case_view.ApiTestEnvironmentDetailView.as_view()),

    # 执行历史
    re_path(r"(?P<project_id>\d+)/api-test/history/$",
            api_test_case_view.ApiTestHistoryListView.as_view()),
    re_path(r"(?P<project_id>\d+)/api-test/history/(?P<history_id>\d+)/$",
            api_test_case_view.ApiTestHistoryDetailView.as_view()),

    # 代码生成
    re_path(r"(?P<project_id>\d+)/api-test/cases/(?P<case_id>\d+)/code-snippet/$",
            api_test_case_view.ApiTestCodeSnippetView.as_view()),

    # 测试场景管理
    re_path(r"(?P<project_id>\d+)/api-test/scenes/$",
            api_test_case_view.ApiTestSceneListView.as_view()),
    re_path(r"(?P<project_id>\d+)/api-test/scenes/(?P<scene_id>\d+)/$",
            api_test_case_view.ApiTestSceneDetailView.as_view()),

    # 场景步骤管理
    re_path(r"(?P<project_id>\d+)/api-test/scenes/(?P<scene_id>\d+)/steps/$",
            api_test_case_view.ApiTestSceneStepListView.as_view()),
    re_path(r"(?P<project_id>\d+)/api-test/scenes/(?P<scene_id>\d+)/steps/(?P<step_id>\d+)/$",
            api_test_case_view.ApiTestSceneStepDetailView.as_view()),

    # 场景执行
    re_path(r"(?P<project_id>\d+)/api-test/scenes/(?P<scene_id>\d+)/execute/$",
            api_test_case_view.ApiTestSceneExecuteView.as_view()),
    re_path(r"(?P<project_id>\d+)/api-test/scenes/(?P<scene_id>\d+)/stop/$",
            api_test_case_view.ApiTestSceneStopView.as_view()),

    # 场景复制
    re_path(r"(?P<project_id>\d+)/api-test/scenes/(?P<scene_id>\d+)/copy/$",
            api_test_case_view.ApiTestSceneCopyView.as_view()),

    # 步骤顺序更新
    re_path(r"(?P<project_id>\d+)/api-test/scenes/(?P<scene_id>\d+)/steps/order/$",
            api_test_case_view.ApiTestSceneStepOrderView.as_view()),

    # 场景执行历史
    re_path(r"(?P<project_id>\d+)/api-test/scenes/history/$",
            api_test_case_view.ApiTestSceneHistoryListView.as_view()),
    re_path(r"(?P<project_id>\d+)/api-test/scenes/history/(?P<history_id>\d+)/$",
            api_test_case_view.ApiTestSceneHistoryDetailView.as_view()),
    re_path(r"(?P<project_id>\d+)/api-test/scenes/history/(?P<history_id>\d+)/steps/$",
            api_test_case_view.ApiTestSceneStepRecordListView.as_view()),
]
```

### 3.2 序列化器设计

#### 3.2.1 集合序列化器

```python
class ApiTestCollectionSerializer(serializers.ModelSerializer):
    children = serializers.SerializerMethodField()
    case_count = serializers.SerializerMethodField()

    def get_children(self, obj):
        if obj.is_folder:
            children = ApiTestCollection.objects.filter(parent=obj.id, IsActive=True)
            return ApiTestCollectionTreeSerializer(children, many=True).data
        return []

    def get_case_count(self, obj):
        return ApiTestCase.objects.filter(collection=obj, IsActive=True).count()

    class Meta:
        model = ApiTestCollection
        fields = ['id', 'name', 'description', 'is_folder', 'children', 'case_count', 'CreationTime']
```

#### 3.2.2 测试用例序列化器

```python
class ApiTestCaseSerializer(serializers.ModelSerializer):
    collection_name = serializers.CharField(source='collection.name', read_only=True)
    creator_name = serializers.SerializerMethodField()

    def get_creator_name(self, obj):
        try:
            user = User.objects.get(id=obj.creator)
            return user.username
        except User.DoesNotExist:
            return "Unknown"

    class Meta:
        model = ApiTestCase
        fields = '__all__'
```

#### 3.2.3 测试场景序列化器

```python
class ApiTestSceneSerializer(serializers.ModelSerializer):
    environment_name = serializers.CharField(source='environment.name', read_only=True)
    creator_name = serializers.SerializerMethodField()
    step_count = serializers.SerializerMethodField()
    last_execution_status = serializers.SerializerMethodField()
    last_execution_time = serializers.SerializerMethodField()

    def get_creator_name(self, obj):
        try:
            user = User.objects.get(id=obj.creator)
            return user.username
        except User.DoesNotExist:
            return "Unknown"

    def get_step_count(self, obj):
        return ApiTestSceneStep.objects.filter(scene=obj, IsActive=True).count()

    def get_last_execution_status(self, obj):
        """获取最近一次执行状态"""
        last_history = ApiTestSceneHistory.objects.filter(
            scene=obj, IsActive=True
        ).order_by('-CreationTime').first()
        return last_history.status if last_history else None

    def get_last_execution_time(self, obj):
        """获取最近一次执行时间"""
        last_history = ApiTestSceneHistory.objects.filter(
            scene=obj, IsActive=True
        ).order_by('-CreationTime').first()
        return last_history.start_time if last_history else None

    class Meta:
        model = ApiTestScene
        fields = '__all__'

class ApiTestSceneStepSerializer(serializers.ModelSerializer):
    test_case_name = serializers.CharField(source='test_case.name', read_only=True)
    test_case_method = serializers.CharField(source='test_case.method', read_only=True)
    test_case_url = serializers.CharField(source='test_case.url', read_only=True)

    def validate(self, data):
        """根据步骤类型验证必填字段"""
        step_type = data.get('step_type')

        if step_type == 'api':
            if not data.get('method') or not data.get('url'):
                raise serializers.ValidationError("API步骤需要指定HTTP方法和URL")
        elif step_type == 'wait':
            if not data.get('wait_time') or data.get('wait_time') <= 0:
                raise serializers.ValidationError("等待步骤需要指定有效的等待时间")
        elif step_type == 'controller':
            if not data.get('condition') or not data.get('operator'):
                raise serializers.ValidationError("控制器步骤需要指定条件表达式和操作符")
        elif step_type == 'case':
            if not data.get('test_case'):
                raise serializers.ValidationError("测试用例步骤需要关联一个测试用例")

        return data

    class Meta:
        model = ApiTestSceneStep
        fields = '__all__'

class ApiTestSceneHistorySerializer(serializers.ModelSerializer):
    scene_name = serializers.CharField(source='scene.name', read_only=True)
    executor_name = serializers.SerializerMethodField()
    success_rate = serializers.SerializerMethodField()

    def get_executor_name(self, obj):
        try:
            user = User.objects.get(id=obj.executor)
            return user.username
        except User.DoesNotExist:
            return "Unknown"

    def get_success_rate(self, obj):
        if obj.total_steps == 0:
            return 0
        return round((obj.success_steps / obj.total_steps) * 100, 2)

    class Meta:
        model = ApiTestSceneHistory
        fields = '__all__'

class ApiTestSceneStepRecordSerializer(serializers.ModelSerializer):
    step_name = serializers.CharField(read_only=True)

    class Meta:
        model = ApiTestSceneStepRecord
        fields = '__all__'
```

### 3.3 视图类设计

#### 3.3.1 集合管理视图

```python
class ApiTestCollectionListView(generics.ListCreateAPIView):
    """
    GET /api/project/{project_id}/api-test/collections/
    POST /api/project/{project_id}/api-test/collections/
    """
    serializer_class = ApiTestCollectionSerializer
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)
    permission_classes = [AllowAny]

    def get_queryset(self):
        project_id = self.kwargs['project_id']
        parent = self.request.GET.get('parent', 0)
        return ApiTestCollection.objects.get_project_collections(project_id, parent)

    def perform_create(self, serializer):
        project_id = self.kwargs['project_id']
        serializer.save(project_id=project_id, creator=self.request.user.id)

class ApiTestCollectionDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    GET/PUT/DELETE /api/project/{project_id}/api-test/collections/{collection_id}/
    """
    serializer_class = ApiTestCollectionSerializer
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)
    permission_classes = [AllowAny]

    def get_object(self):
        collection_id = self.kwargs['collection_id']
        return get_object_or_404(ApiTestCollection, id=collection_id, IsActive=True)
```

#### 3.3.2 测试用例执行视图

```python
class ApiTestCaseExecuteView(APIView):
    """
    POST /api/project/{project_id}/api-test/cases/{case_id}/execute/
    """
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)
    permission_classes = [AllowAny]

    def post(self, request, project_id, case_id):
        try:
            test_case = get_object_or_404(ApiTestCase, id=case_id, IsActive=True)
            environment_id = request.data.get('environment_id')

            # 执行API请求
            executor = ApiTestExecutor(test_case, environment_id)
            result = executor.execute()

            # 保存执行历史
            history = ApiTestHistory.objects.create(
                project_id=project_id,
                test_case=test_case,
                request_snapshot=executor.get_request_snapshot(),
                response_status=result.get('status_code'),
                response_headers=result.get('headers', {}),
                response_body=result.get('body', ''),
                response_time=result.get('response_time'),
                response_size=result.get('response_size'),
                test_results=result.get('test_results', []),
                is_success=result.get('is_success', True),
                error_message=result.get('error_message'),
                executor=request.user.id
            )

            return Response({
                'code': 0,
                'msg': 'success',
                'result': {
                    'history_id': history.id,
                    'response': result
                }
            })

        except Exception as e:
            return Response({
                'code': 1001,
                'msg': str(e),
                'result': None
            }, status=500)
```

#### 3.3.3 测试场景管理视图

```python
class ApiTestSceneListView(generics.ListCreateAPIView):
    """
    GET /api/project/{project_id}/api-test/scenes/
    POST /api/project/{project_id}/api-test/scenes/
    """
    serializer_class = ApiTestSceneSerializer
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)
    permission_classes = [AllowAny]

    def get_queryset(self):
        project_id = self.kwargs['project_id']
        return ApiTestScene.objects.filter(project_id=project_id, IsActive=True).order_by('-CreationTime')

    def perform_create(self, serializer):
        project_id = self.kwargs['project_id']
        serializer.save(project_id=project_id, creator=self.request.user.id)

class ApiTestSceneDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    GET/PUT/DELETE /api/project/{project_id}/api-test/scenes/{scene_id}/
    """
    serializer_class = ApiTestSceneSerializer
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)
    permission_classes = [AllowAny]

    def get_object(self):
        scene_id = self.kwargs['scene_id']
        return get_object_or_404(ApiTestScene, id=scene_id, IsActive=True)

class ApiTestSceneStepListView(generics.ListCreateAPIView):
    """
    GET /api/project/{project_id}/api-test/scenes/{scene_id}/steps/
    POST /api/project/{project_id}/api-test/scenes/{scene_id}/steps/
    """
    serializer_class = ApiTestSceneStepSerializer
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)
    permission_classes = [AllowAny]

    def get_queryset(self):
        scene_id = self.kwargs['scene_id']
        return ApiTestSceneStep.objects.filter(scene_id=scene_id, IsActive=True).order_by('step_order')

    def perform_create(self, serializer):
        scene_id = self.kwargs['scene_id']
        scene = get_object_or_404(ApiTestScene, id=scene_id, IsActive=True)

        # 自动设置步骤顺序
        max_order = ApiTestSceneStep.objects.filter(scene=scene, IsActive=True).aggregate(
            max_order=models.Max('step_order')
        )['max_order'] or 0

        serializer.save(scene=scene, step_order=max_order + 1)

class ApiTestSceneStepDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    GET/PUT/DELETE /api/project/{project_id}/api-test/scenes/{scene_id}/steps/{step_id}/
    """
    serializer_class = ApiTestSceneStepSerializer
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)
    permission_classes = [AllowAny]

    def get_object(self):
        step_id = self.kwargs['step_id']
        return get_object_or_404(ApiTestSceneStep, id=step_id, IsActive=True)

class ApiTestSceneStepOrderView(APIView):
    """
    PUT /api/project/{project_id}/api-test/scenes/{scene_id}/steps/order/
    """
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)
    permission_classes = [AllowAny]

    def put(self, request, project_id, scene_id):
        try:
            scene = get_object_or_404(ApiTestScene, id=scene_id, IsActive=True)
            steps_order = request.data.get('steps', [])

            # 批量更新步骤顺序
            for order_data in steps_order:
                step_id = order_data.get('id')
                new_order = order_data.get('step_order')

                ApiTestSceneStep.objects.filter(
                    id=step_id, scene=scene, IsActive=True
                ).update(step_order=new_order)

            return Response({
                'code': 0,
                'msg': 'success',
                'result': None
            })

        except Exception as e:
            return Response({
                'code': 1001,
                'msg': str(e),
                'result': None
            }, status=500)

class ApiTestSceneCopyView(APIView):
    """
    POST /api/project/{project_id}/api-test/scenes/{scene_id}/copy/
    """
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)
    permission_classes = [AllowAny]

    def post(self, request, project_id, scene_id):
        try:
            original_scene = get_object_or_404(ApiTestScene, id=scene_id, IsActive=True)
            new_name = request.data.get('name', f"{original_scene.name} - 副本")

            # 复制场景
            new_scene = ApiTestScene.objects.create(
                project_id=project_id,
                name=new_name,
                description=original_scene.description,
                module=original_scene.module,
                level=original_scene.level,
                status='progress',  # 新场景默认为进行中状态
                tags=original_scene.tags,
                environment=original_scene.environment,
                global_variables=original_scene.global_variables,
                global_headers=original_scene.global_headers,
                pre_script=original_scene.pre_script,
                post_script=original_scene.post_script,
                assertions=original_scene.assertions,
                settings=original_scene.settings,
                creator=request.user.id
            )

            # 复制步骤
            original_steps = ApiTestSceneStep.objects.filter(
                scene=original_scene, IsActive=True
            ).order_by('step_order')

            for step in original_steps:
                ApiTestSceneStep.objects.create(
                    scene=new_scene,
                    step_name=step.step_name,
                    step_type=step.step_type,
                    step_order=step.step_order,
                    is_enabled=step.is_enabled,
                    description=step.description,
                    method=step.method,
                    url=step.url,
                    headers=step.headers,
                    query_params=step.query_params,
                    body_data=step.body_data,
                    wait_time=step.wait_time,
                    condition=step.condition,
                    operator=step.operator,
                    expected_value=step.expected_value,
                    test_case=step.test_case,
                    override_headers=step.override_headers,
                    override_params=step.override_params,
                    override_body=step.override_body,
                    extract_variables=step.extract_variables,
                    pre_script=step.pre_script,
                    post_script=step.post_script,
                    wait_before=step.wait_before,
                    wait_after=step.wait_after,
                    retry_count=step.retry_count,
                    retry_interval=step.retry_interval
                )

            serializer = ApiTestSceneSerializer(new_scene)
            return Response({
                'code': 0,
                'msg': 'success',
                'result': serializer.data
            })

        except Exception as e:
            return Response({
                'code': 1001,
                'msg': str(e),
                'result': None
            }, status=500)
```

#### 3.3.4 测试场景执行视图

```python
class ApiTestSceneExecuteView(APIView):
    """
    POST /api/project/{project_id}/api-test/scenes/{scene_id}/execute/
    """
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)
    permission_classes = [AllowAny]

    def post(self, request, project_id, scene_id):
        try:
            scene = get_object_or_404(ApiTestScene, id=scene_id, IsActive=True)
            environment_id = request.data.get('environment_id')

            # 创建场景执行历史记录
            scene_history = ApiTestSceneHistory.objects.create(
                project_id=project_id,
                scene=scene,
                scene_snapshot=self._create_scene_snapshot(scene),
                total_steps=0,
                success_steps=0,
                failed_steps=0,
                skipped_steps=0,
                start_time=timezone.now(),
                status='running',
                executor=request.user.id
            )

            # 异步执行场景
            from .tasks import execute_api_test_scene
            execute_api_test_scene.delay(scene_history.id, environment_id)

            return Response({
                'code': 0,
                'msg': 'success',
                'result': {
                    'history_id': scene_history.id,
                    'status': 'running'
                }
            })

        except Exception as e:
            return Response({
                'code': 1001,
                'msg': str(e),
                'result': None
            }, status=500)

    def _create_scene_snapshot(self, scene):
        """创建场景配置快照"""
        steps = ApiTestSceneStep.objects.filter(scene=scene, IsActive=True).order_by('step_order')
        return {
            'scene_id': scene.id,
            'scene_name': scene.name,
            'scene_config': {
                'is_parallel': scene.is_parallel,
                'continue_on_failure': scene.continue_on_failure,
                'global_variables': scene.global_variables,
                'global_headers': scene.global_headers,
            },
            'steps': [
                {
                    'step_id': step.id,
                    'step_name': step.step_name,
                    'step_order': step.step_order,
                    'test_case_id': step.test_case.id,
                    'test_case_name': step.test_case.name,
                    'is_enabled': step.is_enabled,
                    'extract_variables': step.extract_variables,
                }
                for step in steps
            ]
        }

class ApiTestSceneStopView(APIView):
    """
    POST /api/project/{project_id}/api-test/scenes/{scene_id}/stop/
    """
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)
    permission_classes = [AllowAny]

    def post(self, request, project_id, scene_id):
        try:
            scene = get_object_or_404(ApiTestScene, id=scene_id, IsActive=True)
            history_id = request.data.get('history_id')

            if history_id:
                # 停止特定的执行历史
                scene_history = get_object_or_404(
                    ApiTestSceneHistory,
                    id=history_id,
                    scene=scene,
                    IsActive=True
                )
            else:
                # 停止最新的正在执行的场景
                scene_history = ApiTestSceneHistory.objects.filter(
                    scene=scene,
                    status='running',
                    IsActive=True
                ).order_by('-CreationTime').first()

                if not scene_history:
                    return Response({
                        'code': 1002,
                        'msg': '没有正在执行的场景',
                        'result': None
                    })

            # 更新执行状态为已取消
            scene_history.status = 'cancelled'
            scene_history.end_time = timezone.now()
            scene_history.is_success = False
            scene_history.error_message = '用户手动停止执行'

            if scene_history.start_time:
                scene_history.total_duration = int(
                    (scene_history.end_time - scene_history.start_time).total_seconds() * 1000
                )

            scene_history.save()

    def _resolve_variables(self, text):
        """解析文本中的变量引用"""
        if not isinstance(text, str):
            return text

        import re
        pattern = r'\{\{(\w+)\}\}'

        def replace_var(match):
            var_name = match.group(1)
            return str(self.scene_variables.get(var_name, match.group(0)))

        return re.sub(pattern, replace_var, text)

    def _resolve_dict_variables(self, data):
        """解析字典中的变量引用"""
        if isinstance(data, dict):
            return {k: self._resolve_variables(v) for k, v in data.items()}
        elif isinstance(data, list):
            return [self._resolve_variables(item) for item in data]
        else:
            return self._resolve_variables(data)

    def _evaluate_condition_expression(self, expression):
        """评估条件表达式"""
        # 简单实现，可以根据需要扩展
        # 支持 ${variable_name} 格式的变量引用
        import re

        # 提取变量名
        var_match = re.search(r'\$\{(\w+)\}', expression)
        if var_match:
            var_name = var_match.group(1)
            return self.scene_variables.get(var_name)

        # 如果不是变量引用，直接返回表达式本身
        return expression

    def _compare_values(self, actual, expected, operator):
        """比较两个值"""
        try:
            if operator == 'equals':
                return str(actual) == str(expected)
            elif operator == 'not_equals':
                return str(actual) != str(expected)
            elif operator == 'greater_than':
                return float(actual) > float(expected)
            elif operator == 'less_than':
                return float(actual) < float(expected)
            elif operator == 'contains':
                return str(expected) in str(actual)
            else:
                return False
        except (ValueError, TypeError):
            return False

    def _apply_scene_variables_to_executor(self, executor):
        """将场景变量应用到执行器"""
        # 这里需要根据ApiTestExecutor的实现来调整
        # 假设执行器有设置变量的方法
        if hasattr(executor, 'set_variables'):
            executor.set_variables(self.scene_variables)

            # 更新正在执行的步骤状态
            ApiTestSceneStepRecord.objects.filter(
                scene_history=scene_history,
                status='running'
            ).update(
                status='cancelled',
                end_time=timezone.now(),
                error_message='场景执行被停止'
            )

            return Response({
                'code': 0,
                'msg': 'success',
                'result': {
                    'history_id': scene_history.id,
                    'status': 'cancelled'
                }
            })

        except Exception as e:
            return Response({
                'code': 1001,
                'msg': str(e),
                'result': None
            }, status=500)

class ApiTestSceneStepRecordListView(generics.ListAPIView):
    """
    GET /api/project/{project_id}/api-test/scenes/history/{history_id}/steps/
    """
    serializer_class = ApiTestSceneStepRecordSerializer
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)
    permission_classes = [AllowAny]

    def get_queryset(self):
        history_id = self.kwargs['history_id']
        return ApiTestSceneStepRecord.objects.filter(
            scene_history_id=history_id,
            IsActive=True
        ).order_by('step_order')
```

## 4. 核心业务逻辑

### 4.1 API 请求执行器

#### 4.1.1 ApiTestExecutor 类

```python
import requests
import json
import time
from typing import Dict, Any, Optional

class ApiTestExecutor:
    """API测试执行器"""

    def __init__(self, test_case: ApiTestCase, environment_id: Optional[int] = None):
        self.test_case = test_case
        self.environment = self._load_environment(environment_id)
        self.session = requests.Session()

    def execute(self) -> Dict[str, Any]:
        """执行API测试"""
        try:
            # 1. 执行前置脚本
            self._execute_pre_script()

            # 2. 构建请求
            request_config = self._build_request()

            # 3. 发送请求
            start_time = time.time()
            response = self.session.request(**request_config)
            end_time = time.time()

            # 4. 处理响应
            result = self._process_response(response, end_time - start_time)

            # 5. 执行后置脚本和断言
            test_results = self._execute_post_script_and_assertions(response)
            result['test_results'] = test_results
            result['is_success'] = all(t.get('passed', False) for t in test_results)

            return result
```

### 4.4 API 测试场景执行器

#### 4.4.1 ApiTestSceneExecutor 类

```python
import asyncio
import concurrent.futures
from typing import Dict, List, Any, Optional
from django.utils import timezone

class ApiTestSceneExecutor:
    """API测试场景执行器"""

    def __init__(self, scene_history_id: int, environment_id: Optional[int] = None):
        self.scene_history = ApiTestSceneHistory.objects.get(id=scene_history_id)
        self.scene = self.scene_history.scene
        self.environment_id = environment_id
        self.environment = self._load_environment(environment_id)
        self.scene_variables = {}  # 场景级变量存储

    def execute(self):
        """执行测试场景"""
        try:
            # 初始化场景变量
            self.scene_variables.update(self.scene.global_variables)

            # 获取所有步骤
            steps = ApiTestSceneStep.objects.filter(
                scene=self.scene,
                IsActive=True,
                is_enabled=True
            ).order_by('step_order')

            # 更新总步骤数
            self.scene_history.total_steps = steps.count()
            self.scene_history.save()

            if self.scene.is_parallel:
                self._execute_parallel(steps)
            else:
                self._execute_sequential(steps)

            # 更新执行完成状态
            self._finalize_execution()

        except Exception as e:
            self._handle_execution_error(str(e))

    def _execute_sequential(self, steps):
        """顺序执行步骤"""
        for step in steps:
            if not self._should_continue_execution():
                break

            self._execute_single_step(step)

    def _execute_parallel(self, steps):
        """并行执行步骤"""
        with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
            futures = []
            for step in steps:
                future = executor.submit(self._execute_single_step, step)
                futures.append(future)

            # 等待所有步骤完成
            concurrent.futures.wait(futures)

    def _execute_single_step(self, step: ApiTestSceneStep):
        """执行单个步骤"""
        # 创建步骤执行记录
        step_record = ApiTestSceneStepRecord.objects.create(
            scene_history=self.scene_history,
            step=step,
            step_name=step.step_name,
            step_order=step.step_order,
            status='running',
            start_time=timezone.now()
        )

        try:
            # 等待前置时间
            if step.wait_before > 0:
                time.sleep(step.wait_before / 1000.0)

            # 执行前置脚本
            self._execute_pre_script(step, step_record)

            # 根据步骤类型执行不同逻辑
            result = self._execute_step_by_type(step, step_record)

            # 提取变量
            self._extract_variables(step, result, step_record)

            # 执行后置脚本和断言
            self._execute_post_script_and_assertions(step, result, step_record)

            # 等待后置时间
            if step.wait_after > 0:
                time.sleep(step.wait_after / 1000.0)

            # 更新成功状态
            step_record.status = 'success'
            step_record.end_time = timezone.now()
            step_record.duration = int((step_record.end_time - step_record.start_time).total_seconds() * 1000)
            step_record.save()

            # 更新场景统计
            self.scene_history.success_steps += 1
            self.scene_history.save()

        except Exception as e:
            self._handle_step_error(step_record, str(e))

    def _execute_step_by_type(self, step: ApiTestSceneStep, step_record: ApiTestSceneStepRecord):
        """根据步骤类型执行不同的逻辑"""
        if step.step_type == 'api':
            return self._execute_api_step(step, step_record)
        elif step.step_type == 'wait':
            return self._execute_wait_step(step, step_record)
        elif step.step_type == 'controller':
            return self._execute_controller_step(step, step_record)
        elif step.step_type == 'case':
            return self._execute_case_step(step, step_record)
        else:
            raise ValueError(f"不支持的步骤类型: {step.step_type}")

    def _execute_api_step(self, step: ApiTestSceneStep, step_record: ApiTestSceneStepRecord):
        """执行API步骤"""
        # 构建请求配置
        request_config = {
            'method': step.method,
            'url': self._resolve_variables(step.url),
            'headers': self._resolve_dict_variables(step.headers),
            'params': self._resolve_dict_variables(step.query_params),
        }

        # 处理请求体
        if step.body_data:
            if step.method.upper() in ['POST', 'PUT', 'PATCH']:
                request_config['json'] = self._resolve_dict_variables(step.body_data)

        # 应用步骤级别的参数覆盖
        if step.override_headers:
            request_config['headers'].update(self._resolve_dict_variables(step.override_headers))
        if step.override_params:
            request_config['params'].update(self._resolve_dict_variables(step.override_params))
        if step.override_body:
            request_config['json'] = self._resolve_dict_variables(step.override_body)

        # 发送请求
        import requests
        import time

        start_time = time.time()
        response = requests.request(**request_config)
        end_time = time.time()

        # 保存请求响应信息
        step_record.request_snapshot = request_config
        step_record.response_status = response.status_code
        step_record.response_headers = dict(response.headers)
        step_record.response_body = response.text
        step_record.response_time = int((end_time - start_time) * 1000)
        step_record.save()

        return {
            'status_code': response.status_code,
            'headers': dict(response.headers),
            'body': response.text,
            'response_time': int((end_time - start_time) * 1000)
        }

    def _execute_wait_step(self, step: ApiTestSceneStep, step_record: ApiTestSceneStepRecord):
        """执行等待步骤"""
        import time

        wait_time_ms = step.wait_time or 0
        if wait_time_ms > 0:
            time.sleep(wait_time_ms / 1000.0)

        step_record.response_body = f"等待 {wait_time_ms} 毫秒"
        step_record.save()

        return {
            'status_code': 200,
            'body': f"等待 {wait_time_ms} 毫秒完成",
            'wait_time': wait_time_ms
        }

    def _execute_controller_step(self, step: ApiTestSceneStep, step_record: ApiTestSceneStepRecord):
        """执行控制器步骤"""
        condition = self._resolve_variables(step.condition or '')
        operator = step.operator or 'equals'
        expected_value = self._resolve_variables(step.expected_value or '')

        # 简单的条件判断逻辑
        try:
            # 这里可以实现更复杂的条件判断逻辑
            # 例如解析 ${response.status} 这样的表达式
            actual_value = self._evaluate_condition_expression(condition)

            result = self._compare_values(actual_value, expected_value, operator)

            step_record.response_body = f"条件判断: {condition} {operator} {expected_value} = {result}"
            step_record.save()

            if not result:
                raise Exception(f"条件判断失败: {condition} {operator} {expected_value}")

            return {
                'status_code': 200,
                'body': f"条件判断通过: {condition} {operator} {expected_value}",
                'condition_result': result
            }

        except Exception as e:
            step_record.error_message = str(e)
            step_record.save()
            raise

    def _execute_case_step(self, step: ApiTestSceneStep, step_record: ApiTestSceneStepRecord):
        """执行测试用例步骤"""
        if not step.test_case:
            raise ValueError("测试用例步骤必须关联一个测试用例")

        # 创建测试用例执行器
        executor = ApiTestExecutor(step.test_case, self.environment_id)

        # 应用场景变量
        self._apply_scene_variables_to_executor(executor)

        # 执行测试用例
        result = executor.execute()

        # 保存执行结果
        step_record.request_snapshot = executor.get_request_snapshot()
        step_record.response_status = result.get('status_code')
        step_record.response_headers = result.get('headers', {})
        step_record.response_body = result.get('body', '')
        step_record.response_time = result.get('response_time', 0)
        step_record.assertion_results = result.get('test_results', [])
        step_record.save()

        return result

    def _extract_variables(self, step: ApiTestSceneStep, result: Dict, step_record: ApiTestSceneStepRecord):
        """提取变量"""
        if not step.extract_variables:
            return

        extracted = {}
        response_body = result.get('body', {})

        for extract_config in step.extract_variables:
            var_name = extract_config.get('name')
            source = extract_config.get('source')
            extract_type = extract_config.get('type', 'jsonpath')

            try:
                if extract_type == 'jsonpath':
                    value = self._extract_by_jsonpath(response_body, source)
                elif extract_type == 'regex':
                    value = self._extract_by_regex(str(response_body), source)
                else:
                    continue

                if value is not None:
                    extracted[var_name] = value
                    self.scene_variables[var_name] = value

            except Exception as e:
                print(f"变量提取失败 {var_name}: {str(e)}")

        step_record.extracted_variables = extracted
        step_record.save()

    def _extract_by_jsonpath(self, data, jsonpath):
        """使用JSONPath提取数据"""
        try:
            import jsonpath_ng
            parser = jsonpath_ng.parse(jsonpath)
            matches = parser.find(data)
            return matches[0].value if matches else None
        except:
            return None

    def _extract_by_regex(self, text, pattern):
        """使用正则表达式提取数据"""
        try:
            import re
            match = re.search(pattern, text)
            return match.group(1) if match and match.groups() else match.group(0) if match else None
        except:
            return None

    def _should_continue_execution(self):
        """判断是否应该继续执行"""
        if not self.scene.continue_on_failure:
            # 检查是否有失败的步骤
            return self.scene_history.failed_steps == 0
        return True

    def _finalize_execution(self):
        """完成执行，更新最终状态"""
        self.scene_history.end_time = timezone.now()
        self.scene_history.total_duration = int(
            (self.scene_history.end_time - self.scene_history.start_time).total_seconds() * 1000
        )

        if self.scene_history.failed_steps == 0:
            self.scene_history.status = 'completed'
            self.scene_history.is_success = True
        else:
            self.scene_history.status = 'failed'
            self.scene_history.is_success = False

        self.scene_history.save()

    def _handle_step_error(self, step_record: ApiTestSceneStepRecord, error_message: str):
        """处理步骤执行错误"""
        step_record.status = 'failed'
        step_record.error_message = error_message
        step_record.end_time = timezone.now()
        step_record.duration = int((step_record.end_time - step_record.start_time).total_seconds() * 1000)
        step_record.save()

        # 更新场景统计
        self.scene_history.failed_steps += 1
        self.scene_history.save()

    def _handle_execution_error(self, error_message: str):
        """处理场景执行错误"""
        self.scene_history.status = 'failed'
        self.scene_history.is_success = False
        self.scene_history.error_message = error_message
        self.scene_history.end_time = timezone.now()
        if self.scene_history.start_time:
            self.scene_history.total_duration = int(
                (self.scene_history.end_time - self.scene_history.start_time).total_seconds() * 1000
            )
        self.scene_history.save()

    def _resolve_variables(self, text):
        """解析文本中的变量引用"""
        if not isinstance(text, str):
            return text

        import re
        pattern = r'\{\{(\w+)\}\}'

        def replace_var(match):
            var_name = match.group(1)
            return str(self.scene_variables.get(var_name, match.group(0)))

        return re.sub(pattern, replace_var, text)

    def _resolve_dict_variables(self, data):
        """解析字典中的变量引用"""
        if isinstance(data, dict):
            return {k: self._resolve_variables(v) for k, v in data.items()}
        elif isinstance(data, list):
            return [self._resolve_variables(item) for item in data]
        else:
            return self._resolve_variables(data)

    def _evaluate_condition_expression(self, expression):
        """评估条件表达式"""
        # 简单实现，可以根据需要扩展
        # 支持 ${variable_name} 格式的变量引用
        import re

        # 提取变量名
        var_match = re.search(r'\$\{(\w+)\}', expression)
        if var_match:
            var_name = var_match.group(1)
            return self.scene_variables.get(var_name)

        # 如果不是变量引用，直接返回表达式本身
        return expression

    def _compare_values(self, actual, expected, operator):
        """比较两个值"""
        try:
            if operator == 'equals':
                return str(actual) == str(expected)
            elif operator == 'not_equals':
                return str(actual) != str(expected)
            elif operator == 'greater_than':
                return float(actual) > float(expected)
            elif operator == 'less_than':
                return float(actual) < float(expected)
            elif operator == 'contains':
                return str(expected) in str(actual)
            else:
                return False
        except (ValueError, TypeError):
            return False

    def _apply_scene_variables_to_executor(self, executor):
        """将场景变量应用到执行器"""
        # 这里需要根据ApiTestExecutor的实现来调整
        # 假设执行器有设置变量的方法
        if hasattr(executor, 'set_variables'):
            executor.set_variables(self.scene_variables)
```

## 5. 部署和配置

### 5.1 环境配置

#### 5.1.1 开发环境配置

```python
# settings/development.py
CELERY_BROKER_URL = 'redis://localhost:6379/0'
CELERY_RESULT_BACKEND = 'redis://localhost:6379/0'

# API测试相关配置
API_TEST_CONFIG = {
    'MAX_CONCURRENT_REQUESTS': 10,
    'REQUEST_TIMEOUT': 30,
    'MAX_HISTORY_RECORDS': 1000,
    'ENABLE_SCRIPT_EXECUTION': True,
}
```

#### 5.1.2 生产环境配置

```python
# settings/production.py
CELERY_BROKER_URL = os.environ.get('REDIS_URL', 'redis://redis:6379/0')
CELERY_RESULT_BACKEND = os.environ.get('REDIS_URL', 'redis://redis:6379/0')

# 安全配置
API_TEST_CONFIG = {
    'MAX_CONCURRENT_REQUESTS': 5,
    'REQUEST_TIMEOUT': 60,
    'MAX_HISTORY_RECORDS': 5000,
    'ENABLE_SCRIPT_EXECUTION': False,  # 生产环境禁用脚本执行
}
```

### 5.2 数据库迁移

```bash
# 创建迁移文件
python manage.py makemigrations project

# 执行迁移
python manage.py migrate
```

### 5.3 Celery 配置

```python
# celery.py
from celery import Celery
import os

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'teamvision.settings')

app = Celery('teamvision')
app.config_from_object('django.conf:settings', namespace='CELERY')
app.autodiscover_tasks()
```

## 5. API 测试场景功能特性

### 5.1 核心功能点

根据前端界面分析，API 测试场景(ApiTestScene)包含以下具体功能：

#### 5.1.1 场景管理功能

- **场景列表展示**: 显示项目下所有测试场景，包括场景名称、描述、执行状态等
- **场景创建编辑**: 支持创建新场景，编辑场景基本信息（名称、描述、环境配置等）
- **场景删除**: 支持删除不需要的测试场景
- **场景复制**: 支持复制现有场景快速创建新场景

#### 5.1.2 测试步骤管理

- **步骤列表管理**: 显示场景中包含的所有测试步骤，支持拖拽排序
- **步骤添加**: 从现有 API 测试用例中选择添加到场景步骤
- **步骤配置**:
  - 步骤名称自定义
  - 参数覆盖（请求头、查询参数、请求体）
  - 前置/后置脚本配置
  - 等待时间设置
  - 重试配置
- **步骤启用/禁用**: 支持临时禁用某些步骤而不删除

#### 5.1.3 数据传递功能

- **变量提取**: 从 API 响应中提取数据保存为变量
  - 支持 JSONPath 表达式提取
  - 支持正则表达式提取
  - 支持响应头提取
- **变量引用**: 在后续步骤中引用前面步骤提取的变量
- **全局变量**: 场景级别的全局变量配置
- **环境变量**: 支持选择不同环境的变量配置

#### 5.1.4 执行控制功能

- **执行模式选择**:
  - 顺序执行：按步骤顺序依次执行
  - 并行执行：多个步骤同时执行（适用于独立的 API 调用）
- **失败处理策略**:
  - 失败时继续执行后续步骤
  - 失败时停止整个场景执行
- **执行环境选择**: 可选择不同的环境配置执行场景

#### 5.1.5 结果查看功能

- **实时执行状态**: 显示场景执行的实时状态和进度
- **步骤执行结果**: 查看每个步骤的执行结果
  - 请求详情（URL、方法、参数等）
  - 响应详情（状态码、响应体、响应时间等）
  - 断言结果
  - 提取的变量值
- **执行历史记录**: 保存和查看历史执行记录
- **统计信息**: 成功率、平均响应时间等统计数据

#### 5.1.6 高级功能

- **条件执行**: 基于前置步骤结果决定是否执行当前步骤
- **循环执行**: 支持数据驱动的循环执行
- **断言增强**: 场景级别的断言配置
- **报告生成**: 生成场景执行报告
- **导入导出**: 支持场景配置的导入导出

### 5.2 技术实现要点

#### 5.2.1 异步执行

```python
# 使用Celery实现异步场景执行
@shared_task
def execute_api_test_scene(scene_history_id, environment_id=None):
    executor = ApiTestSceneExecutor(scene_history_id, environment_id)
    executor.execute()
```

#### 5.2.2 变量解析引擎

```python
class SceneVariableResolver:
    """场景变量解析器"""

    def resolve_text(self, text: str, variables: dict) -> str:
        """解析文本中的变量引用"""
        import re
        pattern = r'\{\{(\w+)\}\}'

        def replace_var(match):
            var_name = match.group(1)
            return str(variables.get(var_name, match.group(0)))

        return re.sub(pattern, replace_var, text)
```

#### 5.2.3 步骤依赖管理

```python
class StepDependencyManager:
    """步骤依赖管理器"""

    def check_dependencies(self, step, scene_variables):
        """检查步骤执行的前置条件"""
        if step.depends_on:
            for dependency in step.depends_on:
                if not self.is_dependency_satisfied(dependency, scene_variables):
                    return False
        return True
```

## 6. 前端场景功能实现详情

### 6.1 前端组件架构

基于前端实现，API 测试场景功能采用了模块化的组件设计：

#### 6.1.1 主要组件结构

```
TestScenePanel.vue (主面板)
├── SceneListPanel.vue (场景列表)
├── SceneFormPanel.vue (场景表单)
│   ├── SceneStepsManager.vue (步骤管理)
│   ├── SceneParamsManager.vue (参数管理)
│   ├── SceneScriptsManager.vue (脚本管理)
│   ├── SceneAssertionsManager.vue (断言管理)
│   ├── SceneExecutionHistory.vue (执行历史)
│   ├── SceneChangeHistory.vue (变更历史)
│   └── SceneSettingsManager.vue (设置管理)
```

#### 6.1.2 场景数据结构

前端场景表单的数据结构：

```javascript
sceneForm: {
  name: '',                    // 场景名称
  module: '',                  // 所属模块
  level: 'P0',                // 场景等级 (P0, P1, P2)
  status: 'progress',         // 场景状态 (progress, completed, pending)
  tags: '',                   // 标签
  description: '',            // 描述
  steps: [],                  // 步骤列表
  global_variables: {},       // 全局变量
  global_headers: {},         // 全局请求头
  pre_script: '',            // 前置脚本
  post_script: '',           // 后置脚本
  assertions: [],            // 断言配置
  settings: {}               // 执行设置
}
```

#### 6.1.3 步骤数据结构

步骤支持四种类型，每种类型有不同的配置：

```javascript
// API步骤
{
  step_type: 'api',
  step_name: '步骤名称',
  method: 'GET',              // HTTP方法
  url: 'https://api.example.com/users',
  headers: {},                // 请求头
  query_params: {},           // 查询参数
  body_data: {},             // 请求体
  is_enabled: true,          // 是否启用
  description: '步骤描述'
}

// 等待步骤
{
  step_type: 'wait',
  step_name: '等待步骤',
  wait_time: 1000,           // 等待时间(毫秒)
  is_enabled: true,
  description: '等待1秒'
}

// 控制器步骤
{
  step_type: 'controller',
  step_name: '条件判断',
  condition: '${response.status}',  // 条件表达式
  operator: 'equals',               // 操作符
  expected_value: '200',            // 期望值
  is_enabled: true,
  description: '判断响应状态码'
}

// 测试用例步骤
{
  step_type: 'case',
  step_name: '引用测试用例',
  test_case_id: 123,         // 关联的测试用例ID
  method: 'POST',            // 继承自测试用例
  url: '/api/login',         // 继承自测试用例
  is_enabled: true,
  description: '执行登录用例'
}
```

### 6.2 前端功能特性

#### 6.2.1 场景管理功能

- **场景列表展示**: 支持表格形式展示场景信息，包括 ID、名称、等级、状态、最后执行结果等
- **搜索和筛选**: 支持按名称搜索，按等级筛选(P0/P1/P2)
- **分页功能**: 支持大量场景的分页展示
- **场景操作**: 编辑、执行、复制、删除等操作

#### 6.2.2 步骤管理功能

- **拖拽排序**: 使用 vuedraggable 实现步骤的拖拽排序
- **多种步骤类型**: 支持 API、等待、控制器、测试用例四种步骤类型
- **步骤启用/禁用**: 可以临时禁用步骤而不删除
- **步骤复制**: 支持复制现有步骤
- **批量操作**: 支持批量启用/禁用步骤

#### 6.2.3 参数管理功能

- **全局变量**: 场景级别的变量定义和管理
- **全局请求头**: 场景级别的通用请求头设置
- **变量引用**: 支持在步骤中引用全局变量

#### 6.2.4 脚本管理功能

- **前置脚本**: 场景执行前的脚本配置
- **后置脚本**: 场景执行后的脚本配置
- **代码编辑器**: 提供代码编辑界面

#### 6.2.5 断言管理功能

- **断言配置**: 场景级别的断言规则设置
- **断言类型**: 支持多种断言类型
- **断言结果**: 显示断言执行结果

#### 6.2.6 执行设置功能

```javascript
settings: {
  execution_mode: 'sequential',    // 执行模式: sequential(顺序), parallel(并行)
  continue_on_failure: true,       // 失败时是否继续执行
  timeout: 30000,                  // 超时时间(毫秒)
  retry_count: 0,                  // 重试次数
  retry_interval: 1000,            // 重试间隔(毫秒)
  notify_on_completion: false,     // 执行完成通知
  notify_on_failure: true          // 失败时通知
}
```

### 6.3 API 接口映射

前端实现的 API 接口与后端路由的对应关系：

| 前端 API 函数                 | 后端路由                                                                       | 功能描述     |
| ----------------------------- | ------------------------------------------------------------------------------ | ------------ |
| `getApiTestScenesApi()`       | `GET /api/project/{project_id}/api-test/scenes/`                               | 获取场景列表 |
| `createApiTestSceneApi()`     | `POST /api/project/{project_id}/api-test/scenes/`                              | 创建场景     |
| `updateApiTestSceneApi()`     | `PUT /api/project/{project_id}/api-test/scenes/{scene_id}/`                    | 更新场景     |
| `deleteApiTestSceneApi()`     | `DELETE /api/project/{project_id}/api-test/scenes/{scene_id}/`                 | 删除场景     |
| `copyApiTestSceneApi()`       | `POST /api/project/{project_id}/api-test/scenes/{scene_id}/copy/`              | 复制场景     |
| `getApiTestSceneStepsApi()`   | `GET /api/project/{project_id}/api-test/scenes/{scene_id}/steps/`              | 获取步骤列表 |
| `createApiTestSceneStepApi()` | `POST /api/project/{project_id}/api-test/scenes/{scene_id}/steps/`             | 创建步骤     |
| `updateApiTestSceneStepApi()` | `PUT /api/project/{project_id}/api-test/scenes/{scene_id}/steps/{step_id}/`    | 更新步骤     |
| `deleteApiTestSceneStepApi()` | `DELETE /api/project/{project_id}/api-test/scenes/{scene_id}/steps/{step_id}/` | 删除步骤     |
| `updateSceneStepsOrderApi()`  | `PUT /api/project/{project_id}/api-test/scenes/{scene_id}/steps/order/`        | 更新步骤顺序 |
| `executeApiTestSceneApi()`    | `POST /api/project/{project_id}/api-test/scenes/{scene_id}/execute/`           | 执行场景     |
| `stopApiTestSceneApi()`       | `POST /api/project/{project_id}/api-test/scenes/{scene_id}/stop/`              | 停止执行     |
| `getApiTestSceneHistoryApi()` | `GET /api/project/{project_id}/api-test/scenes/history/`                       | 获取执行历史 |

### 6.4 技术实现要点

#### 6.4.1 响应式设计

- 支持不同屏幕尺寸的自适应布局
- 移动端友好的交互设计
- 灵活的组件布局切换

#### 6.4.2 用户体验优化

- 拖拽排序的流畅交互
- 实时的表单验证反馈
- 统一的错误处理机制
- 加载状态的友好提示

#### 6.4.3 数据管理

- 组件间的数据传递和同步
- 表单数据的验证和处理
- 状态管理的统一处理

## 7. 高级功能实现

### 7.1 请求拦截器和中间件

#### 7.1.1 请求拦截器

```python
class ApiRequestInterceptor:
    """API请求拦截器"""

    def __init__(self):
        self.interceptors = []

    def add_interceptor(self, interceptor):
        """添加拦截器"""
        self.interceptors.append(interceptor)

    def process_request(self, request_config):
        """处理请求前拦截"""
        for interceptor in self.interceptors:
            request_config = interceptor.before_request(request_config)
        return request_config

    def process_response(self, response, request_config):
        """处理响应后拦截"""
        for interceptor in reversed(self.interceptors):
            response = interceptor.after_response(response, request_config)
        return response

class LoggingInterceptor:
    """日志拦截器"""

    def before_request(self, request_config):
        logger.info(f"发送请求: {request_config['method']} {request_config['url']}")
        return request_config

    def after_response(self, response, request_config):
        logger.info(f"收到响应: {response.status_code} - {len(response.content)} bytes")
        return response

class AuthInterceptor:
    """认证拦截器"""

    def __init__(self, auth_provider):
        self.auth_provider = auth_provider

    def before_request(self, request_config):
        # 自动添加认证信息
        auth_header = self.auth_provider.get_auth_header()
        if auth_header:
            request_config['headers'].update(auth_header)
        return request_config

    def after_response(self, response, request_config):
        # 处理认证失败
        if response.status_code == 401:
            self.auth_provider.refresh_token()
        return response
```

### 14.2 断言引擎增强

#### 14.2.1 高级断言类型

```python
class AssertionEngine:
    """断言引擎"""

    def __init__(self):
        self.assertion_types = {
            'status_code': self._assert_status_code,
            'response_time': self._assert_response_time,
            'json_path': self._assert_json_path,
            'regex': self._assert_regex,
            'schema': self._assert_schema,
            'custom': self._assert_custom
        }

    def execute_assertions(self, response, assertions):
        """执行断言列表"""
        results = []
        for assertion in assertions:
            try:
                result = self._execute_single_assertion(response, assertion)
                results.append(result)
            except Exception as e:
                results.append({
                    'assertion': assertion,
                    'passed': False,
                    'error': str(e),
                    'message': f"断言执行失败: {str(e)}"
                })
        return results

    def _execute_single_assertion(self, response, assertion):
        """执行单个断言"""
        assertion_type = assertion.get('type')
        assertion_func = self.assertion_types.get(assertion_type)

        if not assertion_func:
            raise ValueError(f"不支持的断言类型: {assertion_type}")

        return assertion_func(response, assertion)

    def _assert_status_code(self, response, assertion):
        """状态码断言"""
        expected = assertion.get('expected')
        actual = response.status_code
        operator = assertion.get('operator', 'equals')

        if operator == 'equals':
            passed = actual == expected
        elif operator == 'not_equals':
            passed = actual != expected
        elif operator == 'in':
            passed = actual in expected
        else:
            raise ValueError(f"不支持的操作符: {operator}")

        return {
            'assertion': assertion,
            'passed': passed,
            'expected': expected,
            'actual': actual,
            'message': f"状态码断言: 期望 {expected}, 实际 {actual}"
        }

    def _assert_json_path(self, response, assertion):
        """JSONPath断言"""
        import jsonpath_ng

        json_path = assertion.get('path')
        expected = assertion.get('expected')
        operator = assertion.get('operator', 'equals')

        try:
            response_json = response.json()
            parser = jsonpath_ng.parse(json_path)
            matches = parser.find(response_json)

            if not matches:
                return {
                    'assertion': assertion,
                    'passed': False,
                    'message': f"JSONPath未找到匹配项: {json_path}"
                }

            actual = matches[0].value

            if operator == 'equals':
                passed = actual == expected
            elif operator == 'contains':
                passed = expected in str(actual)
            elif operator == 'exists':
                passed = True
            else:
                raise ValueError(f"不支持的操作符: {operator}")

            return {
                'assertion': assertion,
                'passed': passed,
                'expected': expected,
                'actual': actual,
                'message': f"JSONPath断言: {json_path} = {actual}"
            }

        except Exception as e:
            return {
                'assertion': assertion,
                'passed': False,
                'error': str(e),
                'message': f"JSONPath断言执行失败: {str(e)}"
            }

    def _assert_schema(self, response, assertion):
        """JSON Schema断言"""
        import jsonschema

        try:
            schema = assertion.get('schema')
            response_json = response.json()

            jsonschema.validate(response_json, schema)

            return {
                'assertion': assertion,
                'passed': True,
                'message': "JSON Schema验证通过"
            }

        except jsonschema.ValidationError as e:
            return {
                'assertion': assertion,
                'passed': False,
                'error': str(e),
                'message': f"JSON Schema验证失败: {str(e)}"
            }
```

### 14.3 数据驱动测试

#### 14.3.1 数据源管理

```python
class DataSourceManager:
    """数据源管理器"""

    def __init__(self):
        self.data_sources = {
            'csv': self._load_csv_data,
            'json': self._load_json_data,
            'excel': self._load_excel_data,
            'database': self._load_database_data
        }

    def load_data(self, data_source_config):
        """加载数据源"""
        source_type = data_source_config.get('type')
        loader = self.data_sources.get(source_type)

        if not loader:
            raise ValueError(f"不支持的数据源类型: {source_type}")

        return loader(data_source_config)

    def _load_csv_data(self, config):
        """加载CSV数据"""
        import pandas as pd

        file_path = config.get('file_path')
        encoding = config.get('encoding', 'utf-8')

        df = pd.read_csv(file_path, encoding=encoding)
        return df.to_dict('records')

    def _load_json_data(self, config):
        """加载JSON数据"""
        import json

        file_path = config.get('file_path')

        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)

        return data if isinstance(data, list) else [data]

    def _load_excel_data(self, config):
        """加载Excel数据"""
        import pandas as pd

        file_path = config.get('file_path')
        sheet_name = config.get('sheet_name', 0)

        df = pd.read_excel(file_path, sheet_name=sheet_name)
        return df.to_dict('records')

class DataDrivenSceneExecutor(ApiTestSceneExecutor):
    """数据驱动场景执行器"""

    def __init__(self, scene_history_id, environment_id=None, data_source_config=None):
        super().__init__(scene_history_id, environment_id)
        self.data_source_config = data_source_config
        self.data_source_manager = DataSourceManager()

    def execute(self):
        """执行数据驱动测试"""
        if not self.data_source_config:
            return super().execute()

        try:
            # 加载测试数据
            test_data = self.data_source_manager.load_data(self.data_source_config)

            # 为每组数据执行场景
            for i, data_row in enumerate(test_data):
                self._execute_with_data(data_row, i + 1)

            self._finalize_execution()

        except Exception as e:
            self._handle_execution_error(str(e))

    def _execute_with_data(self, data_row, iteration):
        """使用特定数据执行场景"""
        # 将数据行添加到场景变量中
        iteration_variables = self.scene_variables.copy()
        iteration_variables.update(data_row)
        iteration_variables['__iteration__'] = iteration

        # 获取步骤并执行
        steps = ApiTestSceneStep.objects.filter(
            scene=self.scene,
            IsActive=True,
            is_enabled=True
        ).order_by('step_order')

        for step in steps:
            if not self._should_continue_execution():
                break

            # 使用当前迭代的变量执行步骤
            self._execute_single_step_with_data(step, iteration_variables, iteration)

    def _execute_single_step_with_data(self, step, variables, iteration):
        """使用特定数据执行单个步骤"""
        # 创建步骤执行记录
        step_record = ApiTestSceneStepRecord.objects.create(
            scene_history=self.scene_history,
            step=step,
            step_name=f"{step.step_name} (迭代 {iteration})",
            step_order=step.step_order,
            status='running',
            start_time=timezone.now()
        )

        try:
            # 使用当前迭代的变量执行API请求
            result = self._execute_api_request_with_variables(step, step_record, variables)

            # 更新成功状态
            step_record.status = 'success'
            step_record.end_time = timezone.now()
            step_record.duration = int((step_record.end_time - step_record.start_time).total_seconds() * 1000)
            step_record.save()

            self.scene_history.success_steps += 1
            self.scene_history.save()

        except Exception as e:
            self._handle_step_error(step_record, str(e))
```

## 8. 后端实现要点总结

### 8.1 核心功能实现优先级

基于前端已实现的功能，后端开发应按以下优先级进行：

#### 8.1.1 高优先级 (P0)

1. **场景基础 CRUD 操作**

   - 场景的创建、读取、更新、删除
   - 场景列表查询和分页
   - 场景复制功能

2. **步骤管理功能**

   - 步骤的增删改查
   - 步骤顺序更新
   - 支持四种步骤类型：api、wait、controller、case

3. **场景执行引擎**
   - 基础的场景执行逻辑
   - 步骤按顺序执行
   - 执行历史记录保存

#### 8.1.2 中优先级 (P1)

1. **变量解析系统**

   - 全局变量和请求头的解析
   - 步骤间数据传递
   - 变量引用语法支持

2. **执行控制功能**

   - 场景执行的启动和停止
   - 执行状态的实时更新
   - 失败处理策略

3. **执行历史管理**
   - 执行历史的详细记录
   - 步骤执行记录
   - 执行结果统计

#### 8.1.3 低优先级 (P2)

1. **高级执行功能**

   - 并行执行支持
   - 条件控制器逻辑
   - 脚本执行引擎

2. **断言系统**
   - 多种断言类型支持
   - 断言结果记录
   - 自定义断言规则

### 8.2 数据库设计要点

#### 8.2.1 必须实现的表结构

1. **project_api_test_scene** - 场景主表
2. **project_api_test_scene_step** - 场景步骤表
3. **project_api_test_scene_history** - 场景执行历史表
4. **project_api_test_scene_step_record** - 步骤执行记录表

#### 8.2.2 关键字段说明

- `step_type`: 支持 'api', 'wait', 'controller', 'case' 四种类型
- `level`: 场景等级，支持 'P0', 'P1', 'P2'
- `status`: 场景状态，支持 'progress', 'completed', 'pending'
- `settings`: JSON 字段，存储执行配置

### 8.3 API 接口实现清单

#### 8.3.1 必须实现的接口

```python
# 场景管理
GET    /api/project/{project_id}/api-test/scenes/                    # 场景列表
POST   /api/project/{project_id}/api-test/scenes/                    # 创建场景
GET    /api/project/{project_id}/api-test/scenes/{scene_id}/         # 场景详情
PUT    /api/project/{project_id}/api-test/scenes/{scene_id}/         # 更新场景
DELETE /api/project/{project_id}/api-test/scenes/{scene_id}/         # 删除场景
POST   /api/project/{project_id}/api-test/scenes/{scene_id}/copy/    # 复制场景

# 步骤管理
GET    /api/project/{project_id}/api-test/scenes/{scene_id}/steps/           # 步骤列表
POST   /api/project/{project_id}/api-test/scenes/{scene_id}/steps/           # 创建步骤
GET    /api/project/{project_id}/api-test/scenes/{scene_id}/steps/{step_id}/ # 步骤详情
PUT    /api/project/{project_id}/api-test/scenes/{scene_id}/steps/{step_id}/ # 更新步骤
DELETE /api/project/{project_id}/api-test/scenes/{scene_id}/steps/{step_id}/ # 删除步骤
PUT    /api/project/{project_id}/api-test/scenes/{scene_id}/steps/order/     # 更新顺序

# 场景执行
POST   /api/project/{project_id}/api-test/scenes/{scene_id}/execute/  # 执行场景
POST   /api/project/{project_id}/api-test/scenes/{scene_id}/stop/     # 停止执行

# 执行历史
GET    /api/project/{project_id}/api-test/scenes/history/                    # 历史列表
GET    /api/project/{project_id}/api-test/scenes/history/{history_id}/       # 历史详情
GET    /api/project/{project_id}/api-test/scenes/history/{history_id}/steps/ # 步骤记录
```

### 8.4 技术实现建议

#### 8.4.1 异步执行

- 使用 Celery 实现场景的异步执行
- 支持长时间运行的场景测试
- 提供执行状态的实时查询

#### 8.4.2 变量解析

- 实现变量引用语法解析 `{{variable_name}}`
- 支持 JSONPath 表达式提取响应数据
- 提供变量作用域管理

#### 8.4.3 错误处理

- 完善的异常捕获和错误信息记录
- 友好的错误信息返回给前端
- 执行失败时的回滚机制

#### 8.4.4 性能优化

- 数据库查询优化
- 大量步骤场景的执行优化
- 执行历史数据的清理策略

### 8.5 开发建议

1. **分阶段开发**: 先实现基础的 CRUD 功能，再逐步添加高级功能
2. **测试驱动**: 为每个 API 接口编写单元测试
3. **文档完善**: 及时更新 API 文档和使用说明
4. **性能监控**: 关注场景执行的性能表现
5. **安全考虑**: 对脚本执行等功能进行安全限制

这个更新后的设计文档现在包含了完整的 API 测试场景功能设计，涵盖了从数据模型到业务逻辑的各个方面，并详细描述了前端实现的功能特性，为后端开发提供了清晰的实现指导。

---

## 版本更新记录

### v2.0 (2024-09-17)

- **重大更新**：基于前端场景功能实现，全面更新后端架构设计
- **结构优化**：重新整理章节编号，确保文档结构的逻辑性和连续性
- **内容增强**：
  - 新增第 5 章"部署和配置"，包含环境配置和 Celery 设置
  - 扩展场景功能特性描述，详细说明四种步骤类型
  - 完善前端组件架构和数据结构定义
  - 增加后端实现优先级指导和技术建议
- **功能扩展**：
  - 支持四种步骤类型：api、wait、controller、case
  - 增强变量解析和传递机制
  - 完善执行控制和错误处理逻辑
  - 添加场景复制、停止执行等新功能

### v1.0 (初始版本)

- 基础 API 测试用例功能设计
- 核心数据模型定义
- 基本 API 接口设计
- 简单的执行器实现
