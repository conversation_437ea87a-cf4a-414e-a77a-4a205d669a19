<template>
  <div class="scene-scripts-manager">
    <div class="scripts-section">
      <h4>前置脚本</h4>
      <el-input
        type="textarea"
        v-model="localPreScript"
        placeholder="请输入前置脚本代码"
        :rows="8"
        @input="emitChange"
      ></el-input>
    </div>
    
    <div class="scripts-section">
      <h4>后置脚本</h4>
      <el-input
        type="textarea"
        v-model="localPostScript"
        placeholder="请输入后置脚本代码"
        :rows="8"
        @input="emitChange"
      ></el-input>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SceneScriptsManager',
  props: {
    preScript: {
      type: String,
      default: ''
    },
    postScript: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      localPreScript: '',
      localPostScript: ''
    }
  },
  watch: {
    preScript: {
      handler(newVal) {
        this.localPreScript = newVal
      },
      immediate: true
    },
    postScript: {
      handler(newVal) {
        this.localPostScript = newVal
      },
      immediate: true
    }
  },
  methods: {
    emitChange() {
      this.$emit('scripts-change', {
        preScript: this.localPreScript,
        postScript: this.localPostScript
      })
    }
  }
}
</script>

<style scoped>
.scene-scripts-manager {
  padding: 16px;
}
.scripts-section {
  margin-bottom: 24px;
}
.scripts-section h4 {
  margin-bottom: 12px;
  color: #333;
}
</style>
