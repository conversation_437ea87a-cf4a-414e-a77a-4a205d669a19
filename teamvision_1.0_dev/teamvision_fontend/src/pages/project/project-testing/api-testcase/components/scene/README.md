# API 测试场景功能

## 功能概述

API 测试场景功能允许用户创建和管理复杂的 API 测试流程，支持多个 API 请求的组合执行、数据传递、条件控制等高级功能。

## 主要组件

### 1. TestScenePanel.vue

主面板组件，包含：

- 左侧场景树：展示所有场景的层级结构
- 右侧内容区：场景列表、创建/编辑表单的切换显示

### 2. SceneListPanel.vue

场景列表组件，功能包括：

- 场景表格展示（ID、名称、等级、状态、执行结果等）
- 搜索和筛选功能
- 场景操作（编辑、执行、复制、删除等）
- 分页功能

### 3. SceneFormPanel.vue

场景表单组件，支持：

- 新建/编辑场景的基本信息
- 多标签页内容管理（步骤、参数、脚本、断言、设置等）
- 场景执行和保存功能

### 4. SceneStepsManager.vue ⭐ **已完善**

步骤管理组件，功能包括：

- 步骤列表展示和拖拽排序
- 支持多种步骤类型：
  - API 步骤：HTTP 请求
  - 等待步骤：延时控制
  - 控制器步骤：条件判断和循环
  - 测试用例步骤：引用现有测试用例
  - 脚本步骤：自定义脚本执行
- 步骤的增删改查操作
- **新增导入功能**：
  - 🔄 **导入系统请求**：从项目 API 接口列表导入
  - 📋 **导入测试用例**：从现有用例导入完整配置
  - 🎬 **导入测试场景**：引用其他场景作为步骤
- **增强功能**：
  - 多选项卡导入界面（接口/用例/场景）
  - 支持批量选择和导入
  - 实时搜索和筛选
  - 统计信息显示
  - 完整的错误处理

### 5. 其他管理组件

- **SceneParamsManager.vue**: 全局变量和请求头管理
- **SceneScriptsManager.vue**: 前置/后置脚本管理
- **SceneAssertionsManager.vue**: 断言配置管理
- **SceneExecutionHistory.vue**: 执行历史查看
- **SceneChangeHistory.vue**: 变更历史记录
- **SceneSettingsManager.vue**: 场景执行设置

## API 接口

已在 `src/api/apiTestCase.js` 中添加了完整的 scene 相关 API 接口：

### 场景管理

- `getApiTestScenesApi()` - 获取场景列表
- `createApiTestSceneApi()` - 创建场景
- `updateApiTestSceneApi()` - 更新场景
- `deleteApiTestSceneApi()` - 删除场景
- `copyApiTestSceneApi()` - 复制场景

### 步骤管理

- `getApiTestSceneStepsApi()` - 获取场景步骤
- `createApiTestSceneStepApi()` - 创建步骤
- `updateApiTestSceneStepApi()` - 更新步骤
- `deleteApiTestSceneStepApi()` - 删除步骤
- `updateSceneStepsOrderApi()` - 更新步骤顺序

### 执行相关

- `executeApiTestSceneApi()` - 执行场景
- `stopApiTestSceneApi()` - 停止执行
- `getApiTestSceneHistoryApi()` - 获取执行历史

## 使用方法

1. **访问场景功能**

   - 在 API 测试用例页面，点击左侧导航栏的"Scenes"图标
   - 进入场景管理界面

2. **创建场景**

   - 点击"新建场景集"或右侧的"+"按钮
   - 填写场景基本信息（名称、模块、等级、状态等）
   - 在"步骤"标签页中添加测试步骤
   - 配置参数、脚本、断言等高级选项
   - 点击"保存"完成创建

3. **管理步骤**

   - 支持拖拽排序调整步骤执行顺序
   - 可以启用/禁用特定步骤
   - 支持复制、编辑、删除步骤操作

4. **执行场景**
   - 在场景列表中点击"执行"按钮
   - 或在编辑页面点击"服务端执行"
   - 查看执行历史和结果

## 技术特性

- **响应式设计**: 支持不同屏幕尺寸的自适应布局
- **拖拽排序**: 使用 vuedraggable 实现步骤拖拽排序
- **模块化设计**: 组件高度解耦，便于维护和扩展
- **类型安全**: 完整的 props 类型定义和验证
- **错误处理**: 统一的 API 错误处理机制

## 开发状态

✅ 已完成：

- 基础 UI 框架和布局
- 场景 CRUD 操作
- **步骤管理功能（已完善）**
  - 完整的导入功能（接口/用例/场景）
  - 拖拽排序和批量操作
  - 多种步骤类型支持
  - 响应式设计和用户体验优化
- API 接口定义
- 基本的表单验证

🚧 待完善：

- 后端 API 接口实现
- 场景执行引擎
- 数据传递和变量解析
- 高级断言功能
- 执行报告生成

## 最新更新 (SceneStepsManager.vue)

### 2024 年更新

- ✅ 完善了"添加步骤"的后续流程
- ✅ 实现了"导入系统请求"界面
- ✅ 新增用例和场景导入功能
- ✅ 优化了用户界面和交互体验
- ✅ 添加了完整的错误处理机制
- ✅ 实现了批量导入和选择功能

**主要改进：**

1. **多选项卡导入界面**：支持接口、用例、场景三种类型的导入
2. **批量操作**：支持多选和批量导入
3. **实时搜索**：支持关键词搜索和筛选
4. **用户体验**：响应式设计，清晰的视觉反馈
5. **错误处理**：完善的异常处理和用户提示

## 注意事项

1. 需要安装 vuedraggable 依赖：`npm install vuedraggable@2.24.3`
2. 确保后端 API 接口已实现对应的 scene 相关接口
3. 部分高级功能（如脚本执行、复杂断言）需要后端支持
4. 导入功能需要相应的后端 API 支持（getApiTestCasesApi、batchCreateSceneStepsApi 等）
