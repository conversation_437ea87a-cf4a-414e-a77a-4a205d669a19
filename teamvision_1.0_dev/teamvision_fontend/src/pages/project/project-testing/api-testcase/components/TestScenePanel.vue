<template>
  <div class="test-scene-panel">
    <!-- 场景树面板 -->
    <div class="scene-header">
      <div class="breadcrumb">
        <el-breadcrumb>
          <el-breadcrumb-item>API 测试场景</el-breadcrumb-item>
        </el-breadcrumb>
      </div>
      <el-input v-model="searchKeyword" placeholder="请输入场景名称进行搜索" size="small" class="search-input"
        @input="handleSearch">
        <i slot="prefix" class="el-icon-search"></i>
      </el-input>
      <div class="action-buttons">
        <el-button type="primary" size="small" icon="el-icon-plus" @click="createScene">
          新建场景
        </el-button>
        <el-button size="small" icon="el-icon-upload" @click="importScene">
          导入场景
        </el-button>
      </div>
    </div>

    <div class="scene-tree">
      <div class="scene-section">
        <div class="scene-section-header" @click="toggleSceneSection">
          <i class="el-icon-folder-opened"></i>
          <span class="section-title">全部场景</span>
          <span class="scene-count">({{ totalScenes }})</span>
          <div class="section-actions">
            <el-button type="text" size="mini" icon="el-icon-more" @click.stop="showSectionMenu"></el-button>
          </div>
        </div>

        <div v-show="sectionExpanded" class="scene-list">
          <div v-for="scene in filteredScenes" :key="scene.id"
            :class="['scene-item', { active: selectedSceneId === scene.id }]" @click="selectScene(scene)">
            <span class="scene-name">{{ scene.name }}</span>
            <div class="scene-actions">
              <el-button type="text" size="mini" icon="el-icon-edit" @click.stop="editScene(scene)"></el-button>
              <el-button type="text" size="mini" icon="el-icon-delete" @click.stop="deleteScene(scene)"></el-button>
              <el-button type="text" size="mini" icon="el-icon-more" @click.stop="showSceneMenu(scene)"></el-button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TestScenePanel',
  props: {
    projectID: {
      type: Number,
      required: true
    }
  },
  data() {
    return {
      // 场景数据
      scenes: [],
      totalScenes: 0,

      // 搜索和筛选
      searchKeyword: '',
      filteredScenes: [],

      // 选中状态
      selectedSceneId: null,

      // UI状态
      sectionExpanded: true
    }
  },
  mounted() {
    // 监听来自TestSceneContent的场景数据更新
    this.$root.$on('scenes-loaded', this.updateScenes)
  },
  beforeDestroy() {
    // 清理事件监听
    this.$root.$off('scenes-loaded', this.updateScenes)
  },
  methods: {
    // 更新场景数据（来自TestSceneContent）
    updateScenes(scenes) {
      this.scenes = scenes
      this.totalScenes = scenes.length
      this.filterScenes()
    },

    // 搜索处理
    handleSearch() {
      this.filterScenes()
    },

    // 筛选场景
    filterScenes() {
      if (!this.searchKeyword.trim()) {
        this.filteredScenes = this.scenes
      } else {
        const keyword = this.searchKeyword.toLowerCase()
        this.filteredScenes = this.scenes.filter(scene =>
          scene.name.toLowerCase().includes(keyword) ||
          (scene.description && scene.description.toLowerCase().includes(keyword))
        )
      }
    },

    // 选择场景
    selectScene(scene) {
      this.selectedSceneId = scene.id
      this.$emit('scene-selected', scene)
    },

    // 场景操作 - 通过事件通知TestSceneContent
    createScene() {
      this.$root.$emit('scene-create-requested')
    },

    editScene(scene) {
      this.$root.$emit('scene-edit-requested', scene)
    },

    deleteScene(scene) {
      this.$root.$emit('scene-delete-requested', scene)
    },

    // UI操作
    toggleSceneSection() {
      this.sectionExpanded = !this.sectionExpanded
    },

    importScene() {
      this.$message.info('导入场景功能开发中')
    },

    showSectionMenu() {
      this.$message.info('更多操作功能开发中')
    },

    showSceneMenu(scene) {
      console.log('Scene menu for:', scene)
      this.$message.info('场景菜单功能开发中')
    }
  }
}
</script>

<style scoped>
.test-scene-panel {
  height: 100%;
  background: #fff;
  display: flex;
  flex-direction: column;
}

.scene-header {
  padding: 16px;
  border-bottom: 1px solid #e0e0e0;
}

.breadcrumb {
  font-size: 12px;
  color: #666;
  margin-bottom: 12px;
}

.search-input {
  margin-bottom: 12px;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.scene-tree {
  flex: 1;
  padding: 8px;
  overflow-y: auto;
}

.scene-section-header {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  cursor: pointer;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.scene-section-header:hover {
  background-color: #f5f5f5;
}

.section-title {
  margin-left: 8px;
  font-weight: 500;
}

.scene-count {
  margin-left: 4px;
  color: #666;
  font-size: 12px;
}

.section-actions {
  margin-left: auto;
}

.scene-list {
  margin-left: 20px;
  margin-top: 4px;
}

.scene-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
  margin-bottom: 2px;
}

.scene-item:hover {
  background-color: #f5f5f5;
}

.scene-item.active {
  background-color: #e6f7ff;
  border-color: #1890ff;
}

.scene-name {
  flex: 1;
  font-size: 14px;
  color: #333;
}

.scene-actions {
  opacity: 0;
  transition: opacity 0.2s;
  display: flex;
  gap: 4px;
}

.scene-item:hover .scene-actions {
  opacity: 1;
}
</style>