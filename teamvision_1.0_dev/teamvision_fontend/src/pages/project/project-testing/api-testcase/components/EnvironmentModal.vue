<template>
  <el-dialog :title="isEdit ? '编辑环境变量' : '新建环境变量'" :visible.sync="dialogVisible" width="600px"
    :before-close="handleClose">
    <div class="environment-form">
      <el-form ref="environmentForm" :model="environmentData" :rules="formRules" label-width="80px">
        <el-form-item label="环境名称" prop="label">
          <el-input v-model="environmentData.label" placeholder="请输入环境名称 (e.g., dev, test, prod)" />
        </el-form-item>
        <el-form-item label="环境描述">
          <el-input v-model="environmentData.description" type="textarea" :rows="2" placeholder="请输入环境描述（可选）" />
        </el-form-item>
      </el-form>

      <el-tabs v-model="activeTab">
        <el-tab-pane label="变量" name="variables">
          <div class="variables-section">
            <div class="section-header">
              <span>变量</span>
              <div class="section-actions">
                <el-button @click="pasteVariables">
                  <i class="el-icon-document-copy"></i> 粘贴
                </el-button>
              </div>
            </div>

            <el-table :data="environmentData.variables" style="width: 100%">
              <el-table-column label="Key" width="120">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.key" placeholder="Variable name"
                    :class="{ 'is-error': scope.row.keyError }" @blur="validateVariableKey(scope.$index)" />
                  <div v-if="scope.row.keyError" class="error-message">{{ scope.row.keyError }}</div>
                </template>
              </el-table-column>
              <el-table-column label="Value" width="150">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.value" placeholder="Variable value" />
                </template>
              </el-table-column>
              <el-table-column label="Description">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.description" placeholder="Description" />
                </template>
              </el-table-column>
              <el-table-column label="Actions" width="80">
                <template slot-scope="scope">
                  <el-button type="text" @click="copyVariable(scope.row)" title="Copy">
                    <i class="el-icon-document-copy"></i>
                  </el-button>
                  <el-button type="text" @click="removeVariable(scope.$index)" title="Delete">
                    <i class="el-icon-delete"></i>
                  </el-button>
                </template>
              </el-table-column>
            </el-table>

            <el-button type="dashed" style="width: 100%; margin-top: 8px;" @click="addVariable">
              <i class="el-icon-plus"></i> 添加变量
            </el-button>
          </div>
        </el-tab-pane>

        <el-tab-pane label="密钥" name="secrets">
          <div class="secrets-section">
            <div class="section-header">
              <span>密钥</span>
              <div class="section-actions">
                <el-button @click="pasteSecrets">
                  <i class="el-icon-document-copy"></i> 粘贴
                </el-button>
              </div>
            </div>

            <el-table :data="environmentData.secrets" style="width: 100%">
              <el-table-column label="Key" width="120">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.key" placeholder="Secret name"
                    :class="{ 'is-error': scope.row.keyError }" @blur="validateSecretKey(scope.$index)" />
                  <div v-if="scope.row.keyError" class="error-message">{{ scope.row.keyError }}</div>
                </template>
              </el-table-column>
              <el-table-column label="Value" width="150">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.value" type="password" placeholder="Secret value" show-password />
                </template>
              </el-table-column>
              <el-table-column label="Description">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.description" placeholder="Description" />
                </template>
              </el-table-column>
              <el-table-column label="Actions" width="80">
                <template slot-scope="scope">
                  <el-button type="text" @click="copySecret(scope.row)" title="Copy">
                    <i class="el-icon-document-copy"></i>
                  </el-button>
                  <el-button type="text" @click="removeSecret(scope.$index)" title="Delete">
                    <i class="el-icon-delete"></i>
                  </el-button>
                </template>
              </el-table-column>
            </el-table>

            <el-button type="dashed" style="width: 100%; margin-top: 8px;" @click="addSecret">
              <i class="el-icon-plus"></i> 添加密钥
            </el-button>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="saveEnvironment">保存</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'EnvironmentModal',
  props: ['projectID'],
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    editData: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      activeTab: 'variables',
      environmentData: {
        project_id: this.projectID,
        label: '',
        description: '',
        variables: [],
        secrets: []
      },
      formRules: {
        label: [
          { required: true, message: '请输入环境名称', trigger: 'blur' },
          { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' },
          { pattern: /^[a-zA-Z0-9_-]+$/, message: '环境名称只能包含字母、数字、下划线和连字符', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit('update:visible', val);
      }
    },
    isEdit() {
      return this.editData !== null;
    }
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.initializeData();
      }
    },
    editData: {
      handler() {
        if (this.visible) {
          this.initializeData();
        }
      },
      deep: true
    }
  },
  methods: {
    initializeData() {
      if (this.isEdit && this.editData) {
        // 编辑模式：从editData初始化
        this.environmentData = {
          label: this.editData.name || '',
          description: this.editData.description || '',
          variables: this.convertObjectToArray(this.editData.variables || {}),
          secrets: this.convertObjectToArray(this.editData.secrets || {})
        };
      } else {
        // 新建模式：重置为空
        this.environmentData = {
          project_id: this.projectID,
          label: '',
          description: '',
          variables: [],
          secrets: []
        };
      }
    },
    convertObjectToArray(obj) {
      return Object.entries(obj).map(([key, value]) => ({
        key,
        value,
        description: ''
      }));
    },
    handleClose() {
      this.dialogVisible = false;
    },
    addVariable() {
      this.environmentData.variables.push({
        key: '',
        value: '',
        description: '',
        keyError: ''
      });
    },
    removeVariable(index) {
      this.environmentData.variables.splice(index, 1);
    },
    addSecret() {
      this.environmentData.secrets.push({
        key: '',
        value: '',
        description: '',
        keyError: ''
      });
    },
    removeSecret(index) {
      this.environmentData.secrets.splice(index, 1);
    },
    copyVariable(variable) {
      navigator.clipboard.writeText(`${variable.key}=${variable.value}`);
    },
    copySecret(secret) {
      navigator.clipboard.writeText(`${secret.key}=${secret.value}`);
    },
    pasteVariables() {
      navigator.clipboard.readText().then(text => {
        const lines = text.split('\n');
        lines.forEach(line => {
          const [key, value] = line.split('=');
          if (key && value) {
            this.environmentData.variables.push({
              key: key.trim(),
              value: value.trim(),
              description: ''
            });
          }
        });
      });
    },
    pasteSecrets() {
      navigator.clipboard.readText().then(text => {
        const lines = text.split('\n');
        lines.forEach(line => {
          const [key, value] = line.split('=');
          if (key && value) {
            this.environmentData.secrets.push({
              key: key.trim(),
              value: value.trim(),
              description: ''
            });
          }
        });
      });
    },
    validateVariableKey(index) {
      const variable = this.environmentData.variables[index];
      this.validateKey(variable, 'variables', index);
    },
    validateSecretKey(index) {
      const secret = this.environmentData.secrets[index];
      this.validateKey(secret, 'secrets', index);
    },
    validateKey(item, type, index) {
      const key = item.key.trim();

      // 清除之前的错误
      this.$set(item, 'keyError', '');

      if (!key) {
        return; // 允许空key，但不保存
      }

      // 验证key格式
      if (!/^[a-zA-Z_][a-zA-Z0-9_]*$/.test(key)) {
        this.$set(item, 'keyError', 'Key必须以字母或下划线开头，只能包含字母、数字和下划线');
        return;
      }

      // 检查重复
      const allItems = [...this.environmentData.variables, ...this.environmentData.secrets];
      const duplicates = allItems.filter((item, idx) => {
        if (type === 'variables' && idx === index) return false;
        if (type === 'secrets' && idx === (index + this.environmentData.variables.length)) return false;
        return item.key.trim() === key;
      });

      if (duplicates.length > 0) {
        this.$set(item, 'keyError', 'Key不能重复');
      }
    },
    validateAllKeys() {
      let hasError = false;

      // 验证所有变量key
      this.environmentData.variables.forEach((variable, index) => {
        this.validateVariableKey(index);
        if (variable.keyError) hasError = true;
      });

      // 验证所有密钥key
      this.environmentData.secrets.forEach((secret, index) => {
        this.validateSecretKey(index);
        if (secret.keyError) hasError = true;
      });

      return !hasError;
    },
    saveEnvironment() {
      // 验证基本表单
      this.$refs.environmentForm.validate((valid) => {
        if (!valid) {
          return false;
        }

        // 验证所有key
        if (!this.validateAllKeys()) {
          this.$message.error('请修正变量或密钥的错误');
          return;
        }

        // 过滤掉空的变量和密钥
        const validVariables = this.environmentData.variables.filter(v => v.key.trim());
        const validSecrets = this.environmentData.secrets.filter(s => s.key.trim());

        const saveData = {
          ...this.environmentData,
          variables: validVariables,
          secrets: validSecrets,
          id: this.isEdit ? this.editData.id : undefined
        };

        this.$emit('save', saveData);
        this.dialogVisible = false;
      });
    }
  }
}
</script>

<style scoped>
.environment-form {
  max-height: 60vh;
  overflow-y: auto;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.section-actions {
  display: flex;
  gap: 8px;
}

.variables-section,
.secrets-section {
  margin-top: 16px;
}

.dialog-footer {
  text-align: right;
}

.is-error {
  border-color: #f56c6c !important;
}

.error-message {
  color: #f56c6c;
  font-size: 12px;
  margin-top: 4px;
  line-height: 1;
}
</style>
