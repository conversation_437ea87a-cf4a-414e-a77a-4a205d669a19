<template>
  <div class="history-panel">
    <div class="left-header">
      <div class="breadcrumb">
        <el-breadcrumb>
          <el-breadcrumb-item>历史记录</el-breadcrumb-item>
        </el-breadcrumb>
      </div>

      <el-input v-model="searchQuery" placeholder="搜索历史记录" prefix-icon="el-icon-search" class="search-input" />

      <div class="action-buttons">
        <el-button type="danger" @click="clearHistory">
          <i class="el-icon-delete"></i> Clear
        </el-button>
        <div class="header-icons">
          <el-tooltip content="Export" placement="bottom">
            <el-button icon="el-icon-upload2" circle></el-button>
          </el-tooltip>
        </div>
      </div>
    </div>

    <div class="history-list" v-if="historyItems.length > 0">
      <el-card v-for="item in filteredHistory" :key="item.id" class="history-item" shadow="hover"
        @click.native="selectHistoryItem(item)">
        <div class="history-info">
          <div class="request-details">
            <div class="request-name">
              <el-tag :type="getMethodTagType(item.request_snapshot && item.request_snapshot.method)"
                class="method-tag"> {{ (item.request_snapshot && item.request_snapshot.method) || 'GET' }}
              </el-tag>{{ item.test_case_name || 'Unknown Request' }}
            </div>
            <div class="request-url">{{ (item.request_snapshot && item.request_snapshot.url) || '' }}</div>
            <div class="request-time">
              <i class="el-icon-time"></i>{{ formatTime(item.executed_at) }}
              <span v-if="item.response_time">{{ item.response_time }}ms</span>
              <span v-if="item.response_size">{{ formatSize(item.response_size) }}</span>
            </div>
          </div>
        </div>
        <div class="history-status">
          <el-tag :type="item.is_success ? 'success' : 'danger'" class="status-tag">
            <i :class="item.is_success ? 'el-icon-success' : 'el-icon-error'"></i>
            {{ item.response_status || 'N/A' }}
          </el-tag>
        </div>
      </el-card>
    </div>

    <div v-else class="empty-history">
      <div class="empty-icon">
        <i class="el-icon-time"></i>
      </div>
      <div class="empty-text">暂无历史记录</div>
      <div class="empty-subtext">Start making requests to see them here</div>
    </div>
  </div>
</template>

<script>
import {
  getApiTestHistoryApi,
  getApiTestHistoryDetailApi,
  handleApiError,
  buildUrlParams
} from '@/api/apiTestCase'

export default {
  name: 'HistoryPanel',
  props: {
    projectID: {
      type: Number,
      required: true
    }
  },
  data() {
    return {
      loading: false,
      searchQuery: '',
      historyItems: [],
      currentPage: 1,
      pageSize: 20,
      total: 0,
      selectedCaseId: null
    }
  },
  computed: {
    filteredHistory() {
      if (!this.searchQuery) {
        return this.historyItems
      }

      return this.historyItems.filter(item => {
        const testCaseName = item.test_case_name && item.test_case_name.toLowerCase()
        const requestUrl = item.request_snapshot && item.request_snapshot.url && item.request_snapshot.url.toLowerCase()
        const searchLower = this.searchQuery.toLowerCase()

        return (testCaseName && testCaseName.includes(searchLower)) ||
          (requestUrl && requestUrl.includes(searchLower))
      })
    }
  },
  created() {
    this.loadHistory()
  },
  methods: {
    // 加载历史记录
    async loadHistory() {
      this.loading = true
      try {
        const params = {
          page: this.currentPage,
          limit: this.pageSize
        }
        if (this.selectedCaseId) {
          params.case_id = this.selectedCaseId
        }

        const response = await getApiTestHistoryApi(this.projectID, buildUrlParams(params))

        if (response.data.code) {
          this.historyItems = response.data.result || []
          this.total = result.data.count || 0
        } else {
          this.$message.error(result.message || '加载历史记录失败')
        }
      } catch (error) {
        this.$message.error(handleApiError(error))
      } finally {
        this.loading = false
      }
    },

    // 选择历史记录
    async selectHistoryItem(item) {
      try {
        const response = await getApiTestHistoryDetailApi(this.projectID, item.id)
        if (response.data.code) {
          this.$emit('history-selected', response.data.result)
        } else {
          this.$message.error(response.data.message || '获取历史详情失败')
        }
      } catch (error) {
        this.$message.error(handleApiError(error))
      }
    },

    // 清除历史记录
    clearHistory() {
      this.$confirm('确定要清除所有历史记录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 注意：这里只是清除本地显示，实际删除需要调用后端API
        this.historyItems = []
        this.$message.success('历史记录已清除')
      }).catch(() => {
        this.$message.info('已取消清除')
      })
    },

    // 按测试用例过滤
    filterByCase(caseId) {
      this.selectedCaseId = caseId
      this.currentPage = 1
      this.loadHistory()
    },

    // 刷新历史记录
    refreshHistory() {
      this.loadHistory()
    },

    // 格式化时间
    formatTime(timestamp) {
      if (!timestamp) return ''

      const date = new Date(timestamp)
      const now = new Date()
      const diff = now - date

      if (diff < 1000 * 60) {
        return '刚刚'
      } else if (diff < 1000 * 60 * 60) {
        return `${Math.floor(diff / (1000 * 60))}分钟前`
      } else if (diff < 1000 * 60 * 60 * 24) {
        return `${Math.floor(diff / (1000 * 60 * 60))}小时前`
      } else {
        return date.toLocaleDateString()
      }
    },

    // 格式化文件大小
    formatSize(bytes) {
      if (!bytes) return '0B'

      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(1024))
      return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + sizes[i]
    },

    // 获取HTTP方法标签类型
    getMethodTagType(method) {
      const types = {
        'GET': 'success',
        'POST': 'primary',
        'PUT': 'warning',
        'DELETE': 'danger',
        'PATCH': 'info',
        'HEAD': 'info',
        'OPTIONS': 'info'
      }
      return types[method] || 'success'
    }
  }
}
</script>

<style scoped>
.history-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.left-header {
  padding: 16px;
  border-bottom: 1px solid #e0e0e0;
}

.breadcrumb {
  margin-bottom: 12px;
}

.search-input {
  margin-bottom: 12px;
}

.action-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
  justify-content: space-between;
}

.header-icons {
  display: flex;
  gap: 4px;
  align-items: center;
}

.history-list {
  flex: 1;
  padding: 8px 0px;
  overflow-y: auto;
}

.history-item {
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.history-item>>>.el-card__body {
  padding: 8px;
  display: flex;
  align-items: center;
}

.history-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.method-tag {
  margin-right: 8px;
  font-size: 10px;
}

.request-details {
  flex: 1;
}

.request-name {
  font-size: 12px;
  font-weight: 600;
  color: #333;
  margin-bottom: 2px;
}

.request-url {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.request-time {
  font-size: 11px;
  color: #999;
  display: flex;
  align-items: center;
  gap: 4px;
}

.history-status {
  margin-left: 12px;
}

.status-tag {
  display: flex;
  align-items: center;
  gap: 4px;
}

.empty-history {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
}

.empty-icon {
  font-size: 32px;
  opacity: 0.6;
  margin-bottom: 16px;
  color: #c0c4cc;
}

.empty-text {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.empty-subtext {
  font-size: 12px;
  color: #666;
}
</style>
