<template>
  <div class="scene-assertions-manager">
    <div class="assertions-header">
      <h4>断言配置</h4>
      <el-button type="primary" size="small" @click="addAssertion">添加断言</el-button>
    </div>
    
    <el-table :data="localAssertions" style="width: 100%">
      <el-table-column prop="type" label="断言类型" width="120"></el-table-column>
      <el-table-column prop="target" label="断言目标" width="200"></el-table-column>
      <el-table-column prop="operator" label="操作符" width="100"></el-table-column>
      <el-table-column prop="expected" label="期望值"></el-table-column>
      <el-table-column label="操作" width="120">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="editAssertion(scope.row, scope.$index)">编辑</el-button>
          <el-button type="text" size="small" @click="deleteAssertion(scope.$index)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
export default {
  name: 'SceneAssertionsManager',
  props: {
    assertions: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      localAssertions: []
    }
  },
  watch: {
    assertions: {
      handler(newVal) {
        this.localAssertions = [...newVal]
      },
      immediate: true
    }
  },
  methods: {
    addAssertion() {
      this.$message.info('添加断言功能开发中')
    },
    editAssertion(assertion, index) {
      this.$message.info(`编辑断言功能开发中`)
    },
    deleteAssertion(index) {
      this.localAssertions.splice(index, 1)
      this.$emit('assertions-change', this.localAssertions)
    }
  }
}
</script>

<style scoped>
.scene-assertions-manager {
  padding: 16px;
}
.assertions-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}
.assertions-header h4 {
  margin: 0;
  color: #333;
}
</style>
