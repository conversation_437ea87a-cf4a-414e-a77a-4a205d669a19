# API测试场景功能部署说明

## 概述

本文档说明如何部署和配置API测试场景功能的后端代码。该功能基于Django REST Framework实现，提供完整的API测试场景管理和执行能力。

## 已完成的开发内容

### 1. 数据模型 (Models)
- `ApiTestScene`: API测试场景模型
- `ApiTestSceneStep`: API测试场景步骤模型  
- `ApiTestSceneHistory`: API测试场景执行历史模型
- `ApiTestSceneStepRecord`: API测试场景步骤执行记录模型

### 2. 序列化器 (Serializers)
- `ApiTestSceneSerializer`: 场景序列化器
- `ApiTestSceneListSerializer`: 场景列表序列化器
- `ApiTestSceneStepSerializer`: 场景步骤序列化器
- `ApiTestSceneHistorySerializer`: 场景执行历史序列化器
- `ApiTestSceneHistoryListSerializer`: 场景执行历史列表序列化器
- `ApiTestSceneStepRecordSerializer`: 步骤执行记录序列化器
- `ApiTestSceneExecuteSerializer`: 场景执行请求序列化器
- `ApiTestSceneStepOrderSerializer`: 步骤排序序列化器

### 3. API视图 (Views)
- `ApiTestSceneListView`: 场景列表和创建
- `ApiTestSceneDetailView`: 场景详情、更新、删除
- `ApiTestSceneCopyView`: 场景复制
- `ApiTestSceneStepListView`: 场景步骤列表和创建
- `ApiTestSceneStepDetailView`: 场景步骤详情、更新、删除
- `ApiTestSceneStepOrderView`: 场景步骤排序更新
- `ApiTestSceneExecuteView`: 场景执行
- `ApiTestSceneStopView`: 停止场景执行
- `ApiTestSceneHistoryListView`: 执行历史列表
- `ApiTestSceneHistoryDetailView`: 执行历史详情
- `ApiTestSceneStepRecordListView`: 步骤执行记录列表

### 4. 核心业务逻辑
- `ApiTestSceneExecutor`: API测试场景执行器
- 支持顺序执行和并行执行
- 支持变量提取和传递
- 支持四种步骤类型：api、wait、controller、case

### 5. URL路由配置
- 已配置完整的场景相关API路由
- 已集成到主路由系统中

### 6. 数据库迁移
- 创建了迁移文件 `0004_api_test_scene_models.py`

## 部署步骤

### 1. 环境准备

确保已安装以下依赖（已在requirements.txt中）：
```bash
Django==4.1
djangorestframework==3.14.0
requests==2.31.0
```

### 2. 数据库迁移

执行以下命令创建数据库表：

```bash
cd teamvision
python manage.py migrate project
```

### 3. 重启服务

重启Django应用服务器：
```bash
# 如果使用uwsgi
touch restart.txt

# 或者重启uwsgi进程
sudo systemctl restart teamvision-web
```

### 4. 验证部署

使用提供的测试脚本验证部署是否成功：

```bash
cd teamvision_fontend/src/pages/project/project-testing/api-testcase/
python test_scene_api_endpoints.py
```

或者手动访问以下API端点验证：

```bash
# 获取项目的API测试场景
GET /api/project/{project_id}/api-test/scenes/

# 创建测试场景
POST /api/project/{project_id}/api-test/scenes/

# 执行场景
POST /api/project/{project_id}/api-test/scenes/{scene_id}/execute/

# 获取执行历史
GET /api/project/{project_id}/api-test/scenes/history/
```

## API接口文档

### 场景管理

#### 获取场景列表
```
GET /api/project/{project_id}/api-test/scenes/
```

支持查询参数：
- `level`: 场景等级筛选 (P0/P1/P2)
- `status`: 场景状态筛选 (progress/completed/pending)
- `search`: 搜索关键词

#### 创建场景
```
POST /api/project/{project_id}/api-test/scenes/
Content-Type: application/json

{
    "name": "测试场景",
    "description": "场景描述",
    "module": "所属模块",
    "level": "P0",
    "status": "progress",
    "tags": "标签",
    "global_variables": {
        "base_url": "https://api.example.com"
    },
    "global_headers": {
        "Content-Type": "application/json"
    },
    "settings": {
        "timeout": 30
    },
    "is_parallel": false
}
```

#### 获取场景详情
```
GET /api/project/{project_id}/api-test/scenes/{scene_id}/
```

#### 更新场景
```
PUT /api/project/{project_id}/api-test/scenes/{scene_id}/
Content-Type: application/json

{
    "name": "更新后的场景名称",
    "status": "completed"
}
```

#### 删除场景
```
DELETE /api/project/{project_id}/api-test/scenes/{scene_id}/
```

#### 复制场景
```
POST /api/project/{project_id}/api-test/scenes/{scene_id}/copy/
```

### 场景步骤管理

#### 获取场景步骤
```
GET /api/project/{project_id}/api-test/scenes/{scene_id}/steps/
```

#### 创建场景步骤
```
POST /api/project/{project_id}/api-test/scenes/{scene_id}/steps/
Content-Type: application/json

{
    "step_name": "API请求步骤",
    "description": "步骤描述",
    "step_type": "api",
    "is_enabled": true,
    "method": "GET",
    "url": "{{base_url}}/users",
    "headers": {
        "Authorization": "Bearer {{token}}"
    },
    "variable_extracts": [
        {
            "name": "user_id",
            "type": "jsonpath",
            "expression": "data[0].id"
        }
    ]
}
```

支持的步骤类型：
- `api`: API请求步骤
- `wait`: 等待步骤
- `controller`: 控制器步骤
- `case`: 测试用例步骤

#### 更新步骤顺序
```
PUT /api/project/{project_id}/api-test/scenes/{scene_id}/steps/order/
Content-Type: application/json

{
    "step_orders": {
        "1": 1,
        "2": 2,
        "3": 3
    }
}
```

### 场景执行

#### 执行场景
```
POST /api/project/{project_id}/api-test/scenes/{scene_id}/execute/
Content-Type: application/json

{
    "environment_id": 1,
    "override_variables": {
        "base_url": "https://test-api.example.com"
    }
}
```

#### 停止执行
```
POST /api/project/{project_id}/api-test/scenes/{scene_id}/stop/
Content-Type: application/json

{
    "history_id": 123
}
```

### 执行历史

#### 获取执行历史
```
GET /api/project/{project_id}/api-test/scenes/history/
GET /api/project/{project_id}/api-test/scenes/history/?scene_id=123
```

#### 获取执行历史详情
```
GET /api/project/{project_id}/api-test/scenes/history/{history_id}/
```

#### 获取步骤执行记录
```
GET /api/project/{project_id}/api-test/scenes/history/{history_id}/steps/
```

## 功能特性

### 1. 场景管理
- 支持场景的CRUD操作
- 支持场景复制
- 支持场景分类和标签
- 支持全局变量和请求头配置

### 2. 步骤管理
- 支持四种步骤类型：API、等待、控制器、测试用例
- 支持步骤拖拽排序
- 支持步骤启用/禁用
- 支持变量提取和断言配置

### 3. 场景执行
- 支持顺序执行和并行执行
- 支持变量替换和传递
- 支持执行过程控制（启动、停止）
- 支持执行历史记录

### 4. 变量系统
- 支持全局变量
- 支持环境变量
- 支持步骤间变量传递
- 支持JSONPath变量提取

## 注意事项

1. **数据库迁移**: 确保在部署前执行数据库迁移
2. **权限配置**: 根据需要配置API访问权限
3. **性能优化**: 对于大量步骤的场景，建议优化执行性能
4. **错误处理**: 场景执行过程中的错误会被记录到执行历史中
5. **并发控制**: 同一场景的多次并发执行需要注意资源竞争

## 故障排除

### 常见问题

1. **迁移失败**: 检查数据库连接和权限
2. **API 404错误**: 检查URL路由配置是否正确
3. **执行失败**: 检查场景步骤配置和环境变量
4. **权限错误**: 检查用户认证和权限配置

### 日志查看

查看Django日志获取详细错误信息：
```bash
tail -f /path/to/django/logs/django.log
```

## 后续开发建议

1. **异步执行**: 考虑使用Celery实现场景的异步执行
2. **实时通知**: 添加WebSocket支持实时执行状态通知
3. **报告生成**: 添加执行报告生成功能
4. **性能监控**: 添加执行性能监控和统计
5. **脚本引擎**: 增强前置/后置脚本执行能力
