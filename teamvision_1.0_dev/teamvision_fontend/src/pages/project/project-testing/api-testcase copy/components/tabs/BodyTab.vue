<template>
  <div class="body-tab">
    <div class="body-type-selector">
      <el-radio-group v-model="bodyType" @change="onBodyTypeChange">
        <el-radio-button label="none">None</el-radio-button>
        <el-radio-button label="form-data">Form Data</el-radio-button>
        <el-radio-button label="x-www-form-urlencoded">x-www-form-urlencoded</el-radio-button>
        <el-radio-button label="raw">Raw</el-radio-button>
        <el-radio-button label="binary">Binary</el-radio-button>
      </el-radio-group>
    </div>

    <div class="body-content">
      <!-- None -->
      <div v-if="bodyType === 'none'" class="empty-state">
        <i class="el-icon-info"></i>
        <p>This request does not have a body</p>
      </div>

      <!-- Form Data -->
      <div v-else-if="bodyType === 'form-data'" class="form-data-section">
        <div class="param-table">
          <div class="param-header">
            <div class="param-col param-col-key">Key</div>
            <div class="param-col param-col-value">Value</div>
            <div class="param-col param-col-type">Type</div>
            <div class="param-col param-col-actions">Actions</div>
          </div>
          <div v-for="(item, index) in formData" :key="index" class="param-row">
            <div class="param-col param-col-key">
              <el-input v-model="item.key" placeholder="Key" />
            </div>
            <div class="param-col param-col-value">
              <el-input v-if="item.type === 'text'" v-model="item.value" placeholder="Value" />
              <el-upload v-else class="file-upload" action="#" :auto-upload="false">
                <el-button type="text">Choose File</el-button>
              </el-upload>
            </div>
            <div class="param-col param-col-type">
              <el-select v-model="item.type">
                <el-option label="Text" value="text"></el-option>
                <el-option label="File" value="file"></el-option>
              </el-select>
            </div>
            <div class="param-col param-col-actions">
              <el-button icon="el-icon-delete" type="text" @click="removeFormDataItem(index)" class="delete-btn" />
            </div>
          </div>
          <div class="param-row add-row">
            <el-button icon="el-icon-plus" type="text" @click="addFormDataItem" class="add-btn">
              Add Form Data
            </el-button>
          </div>
        </div>
      </div>

      <!-- URL Encoded -->
      <div v-else-if="bodyType === 'x-www-form-urlencoded'" class="url-encoded-section">
        <div class="param-table">
          <div class="param-header">
            <div class="param-col param-col-key">Key</div>
            <div class="param-col param-col-value">Value</div>
            <div class="param-col param-col-actions">Actions</div>
          </div>
          <div v-for="(item, index) in urlEncodedData" :key="index" class="param-row">
            <div class="param-col param-col-key">
              <el-input v-model="item.key" placeholder="Key" />
            </div>
            <div class="param-col param-col-value">
              <el-input v-model="item.value" placeholder="Value" />
            </div>
            <div class="param-col param-col-actions">
              <el-button icon="el-icon-delete" type="text" @click="removeUrlEncodedItem(index)" class="delete-btn" />
            </div>
          </div>
          <div class="param-row add-row">
            <el-button icon="el-icon-plus" type="text" @click="addUrlEncodedItem" class="add-btn">
              Add Parameter
            </el-button>
          </div>
        </div>
      </div>

      <!-- Raw -->
      <div v-else-if="bodyType === 'raw'" class="raw-section">
        <div class="raw-header">
          <el-select v-model="rawType" class="raw-type-select">
            <el-option label="Text" value="text"></el-option>
            <el-option label="JavaScript" value="javascript"></el-option>
            <el-option label="JSON" value="json"></el-option>
            <el-option label="HTML" value="html"></el-option>
            <el-option label="XML" value="xml"></el-option>
          </el-select>
        </div>
        <div class="raw-editor">
          <el-input v-model="rawContent" type="textarea" :rows="12" placeholder="Enter request body..."
            class="raw-textarea" />
        </div>
      </div>

      <!-- Binary -->
      <div v-else-if="bodyType === 'binary'" class="binary-section">
        <el-upload class="binary-upload" drag action="#" :auto-upload="false">
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">Drop file here or <em>click to upload</em></div>
        </el-upload>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'BodyTab',
  data() {
    return {
      bodyType: 'none',
      rawType: 'json',
      rawContent: '',
      formData: [
        { key: '', value: '', type: 'text' }
      ],
      urlEncodedData: [
        { key: '', value: '' }
      ]
    }
  },
  methods: {
    onBodyTypeChange() {
      this.$emit('body-type-change', this.bodyType);
    },
    addFormDataItem() {
      this.formData.push({ key: '', value: '', type: 'text' });
    },
    removeFormDataItem(index) {
      if (this.formData.length > 1) {
        this.formData.splice(index, 1);
      }
    },
    addUrlEncodedItem() {
      this.urlEncodedData.push({ key: '', value: '' });
    },
    removeUrlEncodedItem(index) {
      if (this.urlEncodedData.length > 1) {
        this.urlEncodedData.splice(index, 1);
      }
    }
  }
}
</script>

<style scoped>
.body-tab {
  padding: 0;
}

.body-type-selector {
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.body-content {
  min-height: 180px;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #909399;
  background: #fafafa;
  border-radius: 4px;
}

.empty-state i {
  font-size: 36px;
  margin-bottom: 12px;
  color: #dcdfe6;
}

.empty-state p {
  font-size: 13px;
  margin: 0;
}

/* 紧凑的参数表格样式 */
.param-table {
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  overflow: hidden;
  background: white;
}

.param-header {
  display: flex;
  background: #fafafa;
  border-bottom: 1px solid #e8e8e8;
  font-weight: 600;
  font-size: 11px;
  color: #606266;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.param-row {
  display: flex;
  border-bottom: 1px solid #f5f5f5;
  transition: background-color 0.2s ease;
  min-height: 36px;
}

.param-row:hover {
  background-color: rgba(24, 144, 255, 0.02);
}

.param-col {
  padding: 6px 10px;
  display: flex;
  align-items: center;
}

.param-col-key {
  flex: 0 0 22%;
  border-right: 1px solid #f5f5f5;
}

.param-col-value {
  flex: 1;
  border-right: 1px solid #f5f5f5;
}

.param-col-type {
  flex: 0 0 100px;
  border-right: 1px solid #f5f5f5;
}

.param-col-actions {
  flex: 0 0 50px;
  justify-content: center;
}

.add-row {
  background: #fafafa;
  padding: 8px 12px;
  justify-content: flex-start;
  border-bottom: none;
}

.add-btn {
  color: #1890ff;
  font-size: 11px;
  padding: 0;
  height: 20px;
  line-height: 20px;
}

.delete-btn {
  color: #f56c6c;
  padding: 2px;
  width: 20px;
  height: 20px;
  font-size: 12px;
}

/* Raw编辑器紧凑样式 */
.raw-section {
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  overflow: hidden;
}

.raw-header {
  background: #fafafa;
  padding: 6px 10px;
  border-bottom: 1px solid #e8e8e8;
  display: flex;
  align-items: center;
}

.raw-type-select {
  width: 100px;
}

.raw-editor {
  padding: 0;
}

.raw-textarea>>>.el-textarea__inner {
  border: none;
  border-radius: 0;
  font-family: 'Monaco', 'Menlo', 'Courier New', monospace;
  font-size: 11px;
  line-height: 1.5;
  padding: 12px;
  resize: vertical;
  min-height: 200px;
}

/* Binary上传紧凑样式 */
.binary-upload {
  margin-top: 12px;
}

.binary-upload>>>.el-upload-dragger {
  border: 2px dashed #d9d9d9;
  border-radius: 4px;
  width: 100%;
  height: 140px;
  text-align: center;
  background: #fafafa;
  transition: all 0.3s ease;
  padding: 20px;
}

.binary-upload>>>.el-upload-dragger:hover {
  border-color: #1890ff;
  background: rgba(24, 144, 255, 0.02);
}

.binary-upload>>>.el-upload__text {
  font-size: 12px;
  color: #606266;
  margin-top: 8px;
}

.binary-upload>>>.el-icon-upload {
  font-size: 36px;
  color: #c0c4cc;
  margin-bottom: 8px;
}

/* 文件上传紧凑样式 */
.file-upload>>>.el-upload {
  display: block;
  width: 100%;
}

.file-upload>>>.el-button--text {
  font-size: 11px;
  padding: 2px 6px;
  height: 22px;
  line-height: 18px;
}

/* 单选按钮组紧凑样式 */
.body-tab>>>.el-radio-button__inner {
  font-size: 11px;
  padding: 6px 12px;
  border-radius: 3px;
  height: 26px;
  line-height: 14px;
}

.body-tab>>>.el-radio-button:first-child .el-radio-button__inner {
  border-radius: 3px 0 0 3px;
}

.body-tab>>>.el-radio-button:last-child .el-radio-button__inner {
  border-radius: 0 3px 3px 0;
}

/* 输入框紧凑样式 */
.body-tab>>>.el-input--mini .el-input__inner {
  height: 28px;
  font-size: 11px;
  border: none;
  background: transparent;
  padding: 0 6px;
}

.body-tab>>>.el-input--mini .el-input__inner:focus {
  border: 1px solid #1890ff;
  background: white;
  border-radius: 3px;
}

.body-tab>>>.el-select--mini .el-input__inner {
  height: 28px;
  font-size: 11px;
  padding: 0 6px;
}

/* 下拉框紧凑样式 */
.body-tab>>>.el-select-dropdown__item {
  font-size: 11px;
  padding: 6px 12px;
  min-height: 28px;
  line-height: 16px;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .param-col-key {
    flex: 0 0 30%;
  }

  .param-col-type {
    flex: 0 0 80px;
  }

  .param-col-actions {
    flex: 0 0 40px;
  }

  .raw-textarea>>>.el-textarea__inner {
    min-height: 150px;
    font-size: 10px;
    padding: 8px;
  }

  .binary-upload>>>.el-upload-dragger {
    height: 120px;
    padding: 15px;
  }

  .body-tab>>>.el-radio-button__inner {
    font-size: 10px;
    padding: 4px 8px;
  }
}

/* 滚动条优化 */
.raw-textarea>>>.el-textarea__inner::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.raw-textarea>>>.el-textarea__inner::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.raw-textarea>>>.el-textarea__inner::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.raw-textarea>>>.el-textarea__inner::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
