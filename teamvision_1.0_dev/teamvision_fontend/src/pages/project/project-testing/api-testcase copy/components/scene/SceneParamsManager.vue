<template>
  <div class="scene-params-manager">
    <div class="params-section">
      <h4>全局变量</h4>
      <el-table :data="variablesList" style="width: 100%">
        <el-table-column prop="key" label="变量名" width="200"></el-table-column>
        <el-table-column prop="value" label="变量值"></el-table-column>
        <el-table-column label="操作" width="100">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="editVariable(scope.row)">编辑</el-button>
            <el-button type="text" size="small" @click="deleteVariable(scope.$index)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-button type="primary" size="small" @click="addVariable" style="margin-top: 10px;">添加变量</el-button>
    </div>
    
    <div class="params-section">
      <h4>全局请求头</h4>
      <el-table :data="headersList" style="width: 100%">
        <el-table-column prop="key" label="Header名" width="200"></el-table-column>
        <el-table-column prop="value" label="Header值"></el-table-column>
        <el-table-column label="操作" width="100">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="editHeader(scope.row)">编辑</el-button>
            <el-button type="text" size="small" @click="deleteHeader(scope.$index)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-button type="primary" size="small" @click="addHeader" style="margin-top: 10px;">添加请求头</el-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SceneParamsManager',
  props: {
    globalVariables: {
      type: Object,
      default: () => ({})
    },
    globalHeaders: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      variablesList: [],
      headersList: []
    }
  },
  watch: {
    globalVariables: {
      handler(newVal) {
        this.variablesList = Object.entries(newVal).map(([key, value]) => ({ key, value }))
      },
      immediate: true
    },
    globalHeaders: {
      handler(newVal) {
        this.headersList = Object.entries(newVal).map(([key, value]) => ({ key, value }))
      },
      immediate: true
    }
  },
  methods: {
    addVariable() {
      this.$message.info('添加变量功能开发中')
    },
    editVariable(variable) {
      this.$message.info(`编辑变量 ${variable.key} 功能开发中`)
    },
    deleteVariable(index) {
      this.variablesList.splice(index, 1)
      this.emitChange()
    },
    addHeader() {
      this.$message.info('添加请求头功能开发中')
    },
    editHeader(header) {
      this.$message.info(`编辑请求头 ${header.key} 功能开发中`)
    },
    deleteHeader(index) {
      this.headersList.splice(index, 1)
      this.emitChange()
    },
    emitChange() {
      const variables = {}
      const headers = {}
      this.variablesList.forEach(item => {
        variables[item.key] = item.value
      })
      this.headersList.forEach(item => {
        headers[item.key] = item.value
      })
      this.$emit('params-change', { variables, headers })
    }
  }
}
</script>

<style scoped>
.scene-params-manager {
  padding: 16px;
}
.params-section {
  margin-bottom: 24px;
}
.params-section h4 {
  margin-bottom: 12px;
  color: #333;
}
</style>
