<template>
  <div class="scene-steps-manager">
    <!-- 步骤头部 -->
    <div class="steps-header">
      <span class="steps-count">共 {{ steps.length }} 个步骤</span>
      <div class="steps-actions">
        <el-button size="small" icon="el-icon-refresh" @click="refreshSteps">刷新</el-button>
      </div>
    </div>

    <!-- 步骤列表 -->
    <div class="steps-container">
      <draggable v-model="localSteps" group="steps" @start="onDragStart" @end="onDragEnd" handle=".step-drag-handle"
        animation="200" ghost-class="step-ghost" chosen-class="step-chosen">
        <div v-for="(step, index) in localSteps" :key="step.id || index"
          :class="['step-item', { disabled: !step.is_enabled }]">
          <!-- 步骤选择和序号 -->
          <div class="step-controls">
            <el-checkbox v-model="step.is_enabled" @change="updateStep(step)"></el-checkbox>
            <span class="step-number">{{ index + 1 }}</span>
            <div class="step-drag-handle">
              <i class="el-icon-s-unfold"></i>
            </div>
          </div>

          <!-- 步骤内容 -->
          <div class="step-content">
            <!-- 步骤类型标签 -->
            <el-tag :type="getStepTypeTagType(step.step_type)" size="small" class="step-type-badge">
              {{ getStepTypeText(step.step_type) }}
            </el-tag>

            <!-- API步骤 -->
            <template v-if="step.step_type === 'api'">
              <el-tag :type="getMethodTagType(step.method)" size="mini" class="step-method-badge">
                {{ step.method }}
              </el-tag>
              <span class="step-description">{{ step.step_name || step.description }}</span>
            </template>

            <!-- 等待步骤 -->
            <template v-if="step.step_type === 'wait'">
              <span class="step-description">等待(ms): {{ step.wait_time || 0 }}</span>
              <span class="step-note">{{ step.step_name || step.description }}</span>
            </template>

            <!-- 控制器步骤 -->
            <template v-if="step.step_type === 'controller'">
              <span class="step-description">{{ step.condition || step.step_name }}</span>
              <span class="step-operator">{{ step.operator || '等于' }}</span>
              <span class="step-value">{{ step.expected_value || '' }}</span>
              <span class="step-note">{{ step.description }}</span>
            </template>

            <!-- 测试用例步骤 -->
            <template v-if="step.step_type === 'case'">
              <el-tag :type="getMethodTagType(step.method)" size="mini" class="step-method-badge">
                {{ step.method }}
              </el-tag>
              <span class="step-description">{{ step.step_name || step.description }}</span>
            </template>
          </div>

          <!-- 步骤操作 -->
          <div class="step-actions">
            <el-button type="text" size="mini" icon="el-icon-edit" @click="editStep(step, index)"
              title="编辑"></el-button>
            <el-button type="text" size="mini" icon="el-icon-delete" @click="deleteStep(step, index)"
              title="删除"></el-button>
            <el-dropdown @command="handleStepCommand" trigger="click">
              <el-button type="text" size="mini" icon="el-icon-more" title="更多"></el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item :command="{ action: 'copy', step, index }">复制</el-dropdown-item>
                <el-dropdown-item :command="{ action: 'run', step, index }">单独执行</el-dropdown-item>
                <el-dropdown-item :command="{ action: 'disable', step, index }">
                  {{ step.is_enabled ? '禁用' : '启用' }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </div>
      </draggable>

      <!-- 添加步骤按钮 -->
      <el-dropdown @command="handleAddStepCommand" trigger="click" placement="top-start">
        <div class="add-step-button">
          <i class="el-icon-plus"></i>
          <span>添加步骤</span>
        </div>
        <el-dropdown-menu slot="dropdown" class="add-step-dropdown">
          <el-dropdown-item disabled class="dropdown-header">
            <span>请求/场景</span>
          </el-dropdown-item>
          <el-dropdown-item command="import_request">
            <i class="el-icon-upload2"></i>
            导入系统请求
          </el-dropdown-item>
          <el-dropdown-item command="custom_request">
            <i class="el-icon-edit"></i>
            自定义请求
          </el-dropdown-item>
          <el-dropdown-item divided disabled class="dropdown-header">
            <span>逻辑控制</span>
          </el-dropdown-item>
          <el-dropdown-item command="loop_controller">
            <i class="el-icon-refresh"></i>
            循环控制器
            <el-tag size="mini" type="info" class="feature-tag">使用教程</el-tag>
          </el-dropdown-item>
          <el-dropdown-item command="condition_controller">
            <i class="el-icon-s-operation"></i>
            条件控制器
          </el-dropdown-item>
          <el-dropdown-item command="if_controller">
            <i class="el-icon-question"></i>
            仅一次控制器
          </el-dropdown-item>
          <el-dropdown-item divided disabled class="dropdown-header">
            <span>其他</span>
          </el-dropdown-item>
          <el-dropdown-item command="script_step">
            <i class="el-icon-document"></i>
            脚本操作
          </el-dropdown-item>
          <el-dropdown-item command="wait_step">
            <i class="el-icon-time"></i>
            等待时间
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>

    <!-- 添加/编辑步骤抽屉 -->
    <Drawer :title="drawerTitle" v-model="showStepDrawer" :width="50" :mask="false" :transfer="false" :inner="true"
      @on-close="resetStepForm">
      <el-form :model="stepForm" :rules="stepFormRules" ref="stepForm" label-width="100px">
        <el-form-item label="步骤类型" prop="step_type">
          <el-select v-model="stepForm.step_type" placeholder="请选择步骤类型" @change="onStepTypeChange"
            :disabled="isEditMode">
            <el-option label="API步骤" value="api"></el-option>
            <el-option label="等待步骤" value="wait"></el-option>
            <el-option label="控制器" value="controller"></el-option>
            <el-option label="测试用例" value="case"></el-option>
            <el-option label="脚本操作" value="script"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="步骤名称" prop="step_name">
          <el-input v-model="stepForm.step_name" placeholder="请输入步骤名称"></el-input>
        </el-form-item>

        <!-- API步骤配置 -->
        <template v-if="stepForm.step_type === 'api'">
          <el-form-item label="HTTP方法" prop="method">
            <el-select v-model="stepForm.method" placeholder="请选择HTTP方法">
              <el-option label="GET" value="GET"></el-option>
              <el-option label="POST" value="POST"></el-option>
              <el-option label="PUT" value="PUT"></el-option>
              <el-option label="DELETE" value="DELETE"></el-option>
              <el-option label="PATCH" value="PATCH"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="请求URL" prop="url">
            <el-input v-model="stepForm.url" placeholder="请输入请求URL"></el-input>
          </el-form-item>
        </template>

        <!-- 等待步骤配置 -->
        <template v-if="stepForm.step_type === 'wait'">
          <el-form-item label="等待时间" prop="wait_time">
            <el-input-number v-model="stepForm.wait_time" :min="0" :max="300000" placeholder="毫秒"
              style="width: 100%"></el-input-number>
          </el-form-item>
        </template>

        <!-- 控制器步骤配置 -->
        <template v-if="stepForm.step_type === 'controller'">
          <el-form-item label="条件表达式" prop="condition">
            <el-input v-model="stepForm.condition" placeholder="例如: ${response.status}"></el-input>
          </el-form-item>
          <el-form-item label="操作符" prop="operator">
            <el-select v-model="stepForm.operator" placeholder="请选择操作符">
              <el-option label="等于" value="equals"></el-option>
              <el-option label="不等于" value="not_equals"></el-option>
              <el-option label="大于" value="greater_than"></el-option>
              <el-option label="小于" value="less_than"></el-option>
              <el-option label="包含" value="contains"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="期望值" prop="expected_value">
            <el-input v-model="stepForm.expected_value" placeholder="请输入期望值"></el-input>
          </el-form-item>
        </template>

        <el-form-item label="描述">
          <el-input type="textarea" v-model="stepForm.description" placeholder="请输入步骤描述" :rows="3"></el-input>
        </el-form-item>
      </el-form>

      <div class="drawer-footer">
        <el-button @click="showStepDrawer = false">取消</el-button>
        <el-button type="primary" @click="saveStep">{{ isEditMode ? '更新' : '添加' }}</el-button>
      </div>
    </Drawer>


  </div>
</template>

<script>
import draggable from 'vuedraggable'

export default {
  name: 'SceneStepsManager',
  components: {
    draggable
  },
  props: {
    projectId: {
      type: Number,
      required: true
    },
    sceneId: {
      type: [Number, String],
      default: null
    },
    steps: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      localSteps: [],
      showStepDrawer: false,
      isEditMode: false,
      editingIndex: -1,

      stepForm: {
        step_type: '',
        step_name: '',
        method: 'GET',
        url: '',
        wait_time: 1000,
        condition: '',
        operator: 'equals',
        expected_value: '',
        description: '',
        is_enabled: true
      },

      stepFormRules: {
        step_type: [
          { required: true, message: '请选择步骤类型', trigger: 'change' }
        ],
        step_name: [
          { required: true, message: '请输入步骤名称', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    drawerTitle() {
      return this.isEditMode ? '编辑步骤' : '添加步骤'
    }
  },
  watch: {
    steps: {
      handler(newSteps) {
        this.localSteps = [...newSteps]
      },
      immediate: true,
      deep: true
    },
    localSteps: {
      handler(newSteps) {
        this.$emit('steps-change', newSteps)
      },
      deep: true
    }
  },
  methods: {
    // 刷新步骤
    refreshSteps() {
      this.$emit('refresh-steps')
    },

    // 拖拽开始
    onDragStart() {
      // 拖拽开始处理
    },

    // 拖拽结束
    onDragEnd() {
      // 更新步骤顺序
      this.localSteps.forEach((step, index) => {
        step.step_order = index + 1
      })
    },

    // 步骤类型变更
    onStepTypeChange() {
      // 重置相关字段
      this.stepForm.method = 'GET'
      this.stepForm.url = ''
      this.stepForm.wait_time = 1000
      this.stepForm.condition = ''
      this.stepForm.operator = 'equals'
      this.stepForm.expected_value = ''
    },

    // 保存步骤（添加或更新）
    saveStep() {
      this.$refs.stepForm.validate((valid) => {
        if (valid) {
          if (this.isEditMode) {
            // 更新现有步骤
            this.$set(this.localSteps, this.editingIndex, { ...this.stepForm })
            this.$message.success('步骤更新成功')
          } else {
            // 添加新步骤
            const newStep = {
              ...this.stepForm,
              id: Date.now(), // 临时ID
              step_order: this.localSteps.length + 1
            }
            this.localSteps.push(newStep)
            this.$message.success('步骤添加成功')
          }
          this.showStepDrawer = false
          this.resetStepForm()
        }
      })
    },

    // 编辑步骤
    editStep(step, index) {
      this.isEditMode = true
      this.editingIndex = index
      this.stepForm = { ...step }
      this.showStepDrawer = true
    },

    // 删除步骤
    deleteStep(step, index) {
      this.$confirm(`确定要删除步骤 "${step.step_name}" 吗？`, '确认删除', {
        type: 'warning'
      }).then(() => {
        this.localSteps.splice(index, 1)
        this.$message.success('步骤删除成功')
      }).catch(() => { })
    },

    // 更新步骤
    updateStep(step) {
      // 步骤更新处理
    },

    // 步骤命令处理
    handleStepCommand(command) {
      const { action, step, index } = command
      switch (action) {
        case 'copy':
          this.copyStep(step, index)
          break
        case 'run':
          this.runStep(step, index)
          break
        case 'disable':
          step.is_enabled = !step.is_enabled
          this.updateStep(step)
          break
      }
    },

    // 处理添加步骤命令
    handleAddStepCommand(command) {
      const stepTypeMap = {
        'import_request': 'api',
        'custom_request': 'api',
        'loop_controller': 'controller',
        'condition_controller': 'controller',
        'if_controller': 'controller',
        'script_step': 'script',
        'wait_step': 'wait'
      };

      const stepType = stepTypeMap[command];
      if (stepType) {
        this.initStepForm(stepType, command);
        this.isEditMode = false;
        this.showStepDrawer = true;
      }
    },

    // 初始化步骤表单
    initStepForm(stepType, command) {
      this.resetStepForm();
      this.stepForm.step_type = stepType;

      // 根据不同的命令设置默认值
      switch (command) {
        case 'import_request':
          this.stepForm.step_name = '导入的系统请求';
          this.stepForm.method = 'GET';
          break;
        case 'custom_request':
          this.stepForm.step_name = '自定义请求';
          this.stepForm.method = 'GET';
          break;
        case 'loop_controller':
          this.stepForm.step_name = '循环控制器';
          this.stepForm.condition = '${loop.count}';
          this.stepForm.operator = 'less_than';
          this.stepForm.expected_value = '10';
          break;
        case 'condition_controller':
          this.stepForm.step_name = '条件控制器';
          this.stepForm.condition = '${response.status}';
          this.stepForm.operator = 'equals';
          this.stepForm.expected_value = '200';
          break;
        case 'if_controller':
          this.stepForm.step_name = '仅一次控制器';
          this.stepForm.condition = '${execution.count}';
          this.stepForm.operator = 'equals';
          this.stepForm.expected_value = '1';
          break;
        case 'script_step':
          this.stepForm.step_name = '脚本操作';
          this.stepForm.step_type = 'script';
          break;
        case 'wait_step':
          this.stepForm.step_name = '等待时间';
          this.stepForm.wait_time = 1000;
          break;
      }
    },

    // 复制步骤
    copyStep(step, index) {
      const copiedStep = {
        ...step,
        id: Date.now(),
        step_name: `${step.step_name} - 副本`,
        step_order: step.step_order + 1
      }
      this.localSteps.splice(index + 1, 0, copiedStep)
    },

    // 单独执行步骤
    runStep(step, index) {
      this.$message.info(`执行步骤 "${step.step_name}" 功能开发中`)
    },

    // 重置步骤表单
    resetStepForm() {
      this.stepForm = {
        step_type: '',
        step_name: '',
        method: 'GET',
        url: '',
        wait_time: 1000,
        condition: '',
        operator: 'equals',
        expected_value: '',
        description: '',
        is_enabled: true
      }
      this.isEditMode = false
      this.editingIndex = -1
      if (this.$refs.stepForm) {
        this.$refs.stepForm.resetFields()
      }
    },

    // 获取步骤类型标签类型
    getStepTypeTagType(stepType) {
      const typeMap = {
        'api': 'primary',
        'wait': 'warning',
        'controller': 'success',
        'case': 'info'
      }
      return typeMap[stepType] || 'default'
    },

    // 获取步骤类型文本
    getStepTypeText(stepType) {
      const textMap = {
        'api': '基础 API',
        'wait': '等待时间',
        'controller': '条件控制器',
        'case': '基础 CASE',
        'script': '脚本操作'
      }
      return textMap[stepType] || stepType
    },

    // 获取HTTP方法标签类型
    getMethodTagType(method) {
      const typeMap = {
        'GET': 'success',
        'POST': 'primary',
        'PUT': 'warning',
        'DELETE': 'danger',
        'PATCH': 'info'
      }
      return typeMap[method] || 'default'
    }
  }
}
</script>

<style scoped>
.scene-steps-manager {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 步骤头部 */
.steps-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 16px;
}

.steps-count {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.steps-actions {
  display: flex;
  gap: 8px;
}

/* 步骤容器 */
.steps-container {
  flex: 1;
  overflow-y: auto;
}

/* 步骤项 */
.step-item {
  display: flex;
  align-items: center;
  padding: 12px;
  margin-bottom: 8px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  background: #fff;
  transition: all 0.2s;
  gap: 12px;
}

.step-item:hover {
  border-color: #d9d9d9;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.step-item.disabled {
  opacity: 0.6;
  background: #f5f5f5;
}

.step-ghost {
  opacity: 0.5;
  background: #e6f7ff;
}

.step-chosen {
  border-color: #1890ff;
}

/* 步骤控制 */
.step-controls {
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 80px;
}

.step-number {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background: #1890ff;
  color: white;
  border-radius: 50%;
  font-size: 12px;
  font-weight: 500;
}

.step-drag-handle {
  cursor: move;
  color: #999;
  padding: 4px;
}

.step-drag-handle:hover {
  color: #666;
}

/* 步骤内容 */
.step-content {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.step-type-badge {
  font-weight: 500;
}

.step-method-badge {
  font-weight: 500;
  font-size: 11px;
}

.step-description {
  font-size: 14px;
  color: #333;
}

.step-operator {
  font-size: 12px;
  color: #666;
  background: #f0f0f0;
  padding: 2px 6px;
  border-radius: 3px;
}

.step-value {
  font-size: 12px;
  color: #1890ff;
  background: #e6f7ff;
  padding: 2px 6px;
  border-radius: 3px;
}

.step-note {
  font-size: 12px;
  color: #999;
  margin-left: auto;
}

/* 步骤操作 */
.step-actions {
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s;
}

.step-item:hover .step-actions {
  opacity: 1;
}

/* 添加步骤按钮 */
.add-step-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 16px;
  margin-top: 16px;
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  color: #666;
  transition: all 0.2s;
}

.add-step-button:hover {
  border-color: #1890ff;
  color: #1890ff;
  background: #f6ffed;
}

/* 添加步骤下拉菜单样式 */
.add-step-dropdown {
  min-width: 200px;
}

.add-step-dropdown .dropdown-header {
  color: #999;
  font-size: 12px;
  font-weight: 600;
  padding: 8px 16px 4px;
  cursor: default;
}

.add-step-dropdown .dropdown-header span {
  color: #666;
}

.add-step-dropdown .el-dropdown-menu__item {
  padding: 8px 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.add-step-dropdown .el-dropdown-menu__item i {
  width: 16px;
  color: #666;
}

.feature-tag {
  margin-left: auto;
  font-size: 10px;
}

/* 抽屉样式 */
.drawer-footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 16px 24px;
  border-top: 1px solid #e8e8e8;
  background: #fff;
  text-align: right;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .step-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .step-controls {
    width: 100%;
    justify-content: space-between;
  }

  .step-content {
    width: 100%;
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .step-actions {
    opacity: 1;
    width: 100%;
    justify-content: flex-end;
  }
}
</style>
