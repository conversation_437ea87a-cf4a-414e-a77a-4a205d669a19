<template>
  <div class="parameters-tab">
    <div class="tab-section">
      <h4 class="section-title">Query Parameters</h4>
      <div class="param-table">
        <div class="param-header">
          <div class="param-col param-col-key">Key</div>
          <div class="param-col param-col-value">Value</div>
          <div class="param-col param-col-description">Description</div>
          <div class="param-col param-col-actions">Actions</div>
        </div>
        <div v-for="(param, index) in queryParams" :key="index" class="param-row">
          <div class="param-col param-col-key">
            <el-input v-model="param.key" placeholder="Parameter name" @input="updateParam" />
          </div>
          <div class="param-col param-col-value">
            <el-input v-model="param.value" placeholder="Parameter value" @input="updateParam" />
          </div>
          <div class="param-col param-col-description">
            <el-input v-model="param.description" placeholder="Description" />
          </div>
          <div class="param-col param-col-actions">
            <el-button icon="el-icon-delete" type="text" @click="removeParam(index)" class="delete-btn" />
          </div>
        </div>
        <div class="param-row add-row">
          <el-button icon="el-icon-plus" type="text" @click="addParam" class="add-btn">
            Add Parameter
          </el-button>
        </div>
      </div>
    </div>

    <div class="tab-section">
      <h4 class="section-title">Path Variables</h4>
      <div class="param-table">
        <div class="param-header">
          <div class="param-col param-col-key">Key</div>
          <div class="param-col param-col-value">Value</div>
          <div class="param-col param-col-description">Description</div>
        </div>
        <div v-for="(variable, index) in pathVariables" :key="index" class="param-row">
          <div class="param-col param-col-key">
            <el-input v-model="variable.key" placeholder="Variable name" disabled />
          </div>
          <div class="param-col param-col-value">
            <el-input v-model="variable.value" placeholder="Variable value" />
          </div>
          <div class="param-col param-col-description">
            <el-input v-model="variable.description" placeholder="Description" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ParametersTab',
  data() {
    return {
      queryParams: [
        { key: '', value: '', description: '' }
      ],
      pathVariables: []
    }
  },
  methods: {
    addParam() {
      this.queryParams.push({ key: '', value: '', description: '' });
    },
    removeParam(index) {
      if (this.queryParams.length > 1) {
        this.queryParams.splice(index, 1);
      }
    },
    updateParam() {
      this.$emit('update-params', {
        query: this.queryParams,
        path: this.pathVariables
      });
    }
  }
}
</script>

<style scoped>
.parameters-tab {
  padding: 0;
}

.tab-section {
  margin-bottom: 24px;
}

.tab-section:last-child {
  margin-bottom: 0;
}

.section-title {
  font-size: 13px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 12px 0;
  padding-bottom: 6px;
  border-bottom: 1px solid #f0f0f0;
}

.param-table {
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  overflow: hidden;
  background: white;
}

.param-header {
  display: flex;
  background: #fafafa;
  border-bottom: 1px solid #e8e8e8;
  font-weight: 600;
  font-size: 11px;
  color: #606266;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.param-row {
  display: flex;
  border-bottom: 1px solid #f5f5f5;
  transition: background-color 0.2s ease;
  min-height: 36px;
}

.param-row:hover {
  background-color: rgba(24, 144, 255, 0.02);
}

.param-row:last-child {
  border-bottom: none;
}

.param-col {
  padding: 6px 10px;
  display: flex;
  align-items: center;
}

.param-col-key {
  flex: 0 0 22%;
  border-right: 1px solid #f5f5f5;
}

.param-col-value {
  flex: 0 0 30%;
  border-right: 1px solid #f5f5f5;
}

.param-col-description {
  flex: 1;
  border-right: 1px solid #f5f5f5;
}

.param-col-actions {
  flex: 0 0 50px;
  justify-content: center;
}

.add-row {
  background: #fafafa;
  padding: 8px 12px;
  justify-content: flex-start;
  border-bottom: none;
}

.add-btn {
  color: #1890ff;
  font-size: 11px;
  padding: 0;
  height: 20px;
  line-height: 20px;
}

.add-btn:hover {
  color: #40a9ff;
}

.delete-btn {
  color: #f56c6c;
  padding: 2px;
  width: 20px;
  height: 20px;
  font-size: 12px;
}

.delete-btn:hover {
  background-color: rgba(245, 108, 108, 0.1);
  border-radius: 2px;
}

.parameters-tab>>>.el-input--mini .el-input__inner {
  height: 28px;
  line-height: 28px;
  font-size: 11px;
  border: none;
  background: transparent;
  padding: 0 6px;
}

.parameters-tab>>>.el-input--mini .el-input__inner:focus {
  border: 1px solid #1890ff;
  background: white;
  border-radius: 3px;
}

.parameters-tab>>>.el-input.is-disabled .el-input__inner {
  background-color: #f5f5f5;
  color: #909399;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .param-col-key {
    flex: 0 0 30%;
  }

  .param-col-value {
    flex: 0 0 35%;
  }

  .param-col-actions {
    flex: 0 0 40px;
  }
}
</style>
