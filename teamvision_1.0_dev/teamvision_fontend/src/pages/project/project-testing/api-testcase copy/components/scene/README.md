# API测试场景功能

## 功能概述

API测试场景功能允许用户创建和管理复杂的API测试流程，支持多个API请求的组合执行、数据传递、条件控制等高级功能。

## 主要组件

### 1. TestScenePanel.vue
主面板组件，包含：
- 左侧场景树：展示所有场景的层级结构
- 右侧内容区：场景列表、创建/编辑表单的切换显示

### 2. SceneListPanel.vue
场景列表组件，功能包括：
- 场景表格展示（ID、名称、等级、状态、执行结果等）
- 搜索和筛选功能
- 场景操作（编辑、执行、复制、删除等）
- 分页功能

### 3. SceneFormPanel.vue
场景表单组件，支持：
- 新建/编辑场景的基本信息
- 多标签页内容管理（步骤、参数、脚本、断言、设置等）
- 场景执行和保存功能

### 4. SceneStepsManager.vue
步骤管理组件，功能包括：
- 步骤列表展示和拖拽排序
- 支持多种步骤类型：
  - API步骤：HTTP请求
  - 等待步骤：延时控制
  - 控制器步骤：条件判断和循环
  - 测试用例步骤：引用现有测试用例
- 步骤的增删改查操作

### 5. 其他管理组件
- **SceneParamsManager.vue**: 全局变量和请求头管理
- **SceneScriptsManager.vue**: 前置/后置脚本管理
- **SceneAssertionsManager.vue**: 断言配置管理
- **SceneExecutionHistory.vue**: 执行历史查看
- **SceneChangeHistory.vue**: 变更历史记录
- **SceneSettingsManager.vue**: 场景执行设置

## API接口

已在 `src/api/apiTestCase.js` 中添加了完整的scene相关API接口：

### 场景管理
- `getApiTestScenesApi()` - 获取场景列表
- `createApiTestSceneApi()` - 创建场景
- `updateApiTestSceneApi()` - 更新场景
- `deleteApiTestSceneApi()` - 删除场景
- `copyApiTestSceneApi()` - 复制场景

### 步骤管理
- `getApiTestSceneStepsApi()` - 获取场景步骤
- `createApiTestSceneStepApi()` - 创建步骤
- `updateApiTestSceneStepApi()` - 更新步骤
- `deleteApiTestSceneStepApi()` - 删除步骤
- `updateSceneStepsOrderApi()` - 更新步骤顺序

### 执行相关
- `executeApiTestSceneApi()` - 执行场景
- `stopApiTestSceneApi()` - 停止执行
- `getApiTestSceneHistoryApi()` - 获取执行历史

## 使用方法

1. **访问场景功能**
   - 在API测试用例页面，点击左侧导航栏的"Scenes"图标
   - 进入场景管理界面

2. **创建场景**
   - 点击"新建场景集"或右侧的"+"按钮
   - 填写场景基本信息（名称、模块、等级、状态等）
   - 在"步骤"标签页中添加测试步骤
   - 配置参数、脚本、断言等高级选项
   - 点击"保存"完成创建

3. **管理步骤**
   - 支持拖拽排序调整步骤执行顺序
   - 可以启用/禁用特定步骤
   - 支持复制、编辑、删除步骤操作

4. **执行场景**
   - 在场景列表中点击"执行"按钮
   - 或在编辑页面点击"服务端执行"
   - 查看执行历史和结果

## 技术特性

- **响应式设计**: 支持不同屏幕尺寸的自适应布局
- **拖拽排序**: 使用vuedraggable实现步骤拖拽排序
- **模块化设计**: 组件高度解耦，便于维护和扩展
- **类型安全**: 完整的props类型定义和验证
- **错误处理**: 统一的API错误处理机制

## 开发状态

✅ 已完成：
- 基础UI框架和布局
- 场景CRUD操作
- 步骤管理功能
- API接口定义
- 基本的表单验证

🚧 待完善：
- 后端API接口实现
- 场景执行引擎
- 数据传递和变量解析
- 高级断言功能
- 执行报告生成

## 注意事项

1. 需要安装vuedraggable依赖：`npm install vuedraggable@2.24.3`
2. 确保后端API接口已实现对应的scene相关接口
3. 部分高级功能（如脚本执行、复杂断言）需要后端支持
