<template>
  <div class="scene-settings-manager">
    <div class="settings-section">
      <h4>执行设置</h4>
      <el-form :model="localSettings" label-width="120px">
        <el-form-item label="执行模式">
          <el-radio-group v-model="localSettings.execution_mode" @change="emitChange">
            <el-radio label="sequential">顺序执行</el-radio>
            <el-radio label="parallel">并行执行</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="失败处理">
          <el-checkbox v-model="localSettings.continue_on_failure" @change="emitChange">
            失败时继续执行
          </el-checkbox>
        </el-form-item>
        
        <el-form-item label="超时设置">
          <el-input-number
            v-model="localSettings.timeout"
            :min="1000"
            :max="300000"
            :step="1000"
            @change="emitChange"
          ></el-input-number>
          <span style="margin-left: 8px; color: #666;">毫秒</span>
        </el-form-item>
        
        <el-form-item label="重试次数">
          <el-input-number
            v-model="localSettings.retry_count"
            :min="0"
            :max="10"
            @change="emitChange"
          ></el-input-number>
        </el-form-item>
        
        <el-form-item label="重试间隔">
          <el-input-number
            v-model="localSettings.retry_interval"
            :min="100"
            :max="10000"
            :step="100"
            @change="emitChange"
          ></el-input-number>
          <span style="margin-left: 8px; color: #666;">毫秒</span>
        </el-form-item>
      </el-form>
    </div>
    
    <div class="settings-section">
      <h4>通知设置</h4>
      <el-form :model="localSettings" label-width="120px">
        <el-form-item label="执行完成通知">
          <el-checkbox v-model="localSettings.notify_on_completion" @change="emitChange">
            启用通知
          </el-checkbox>
        </el-form-item>
        
        <el-form-item label="失败时通知">
          <el-checkbox v-model="localSettings.notify_on_failure" @change="emitChange">
            启用通知
          </el-checkbox>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SceneSettingsManager',
  props: {
    settings: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      localSettings: {
        execution_mode: 'sequential',
        continue_on_failure: true,
        timeout: 30000,
        retry_count: 0,
        retry_interval: 1000,
        notify_on_completion: false,
        notify_on_failure: true
      }
    }
  },
  watch: {
    settings: {
      handler(newVal) {
        this.localSettings = { ...this.localSettings, ...newVal }
      },
      immediate: true
    }
  },
  methods: {
    emitChange() {
      this.$emit('settings-change', this.localSettings)
    }
  }
}
</script>

<style scoped>
.scene-settings-manager {
  padding: 16px;
}
.settings-section {
  margin-bottom: 24px;
  padding: 16px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
}
.settings-section h4 {
  margin-top: 0;
  margin-bottom: 16px;
  color: #333;
}
</style>
