<template>
  <div class="scene-execution-history">
    <div class="history-header">
      <h4>执行历史</h4>
      <el-button type="primary" size="small" @click="refreshHistory">刷新</el-button>
    </div>
    
    <el-table :data="historyList" v-loading="loading" style="width: 100%">
      <el-table-column prop="id" label="执行ID" width="80"></el-table-column>
      <el-table-column prop="status" label="状态" width="100">
        <template slot-scope="scope">
          <el-tag :type="getStatusTagType(scope.row.status)" size="small">
            {{ getStatusText(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="start_time" label="开始时间" width="160"></el-table-column>
      <el-table-column prop="duration" label="耗时(ms)" width="100"></el-table-column>
      <el-table-column prop="success_rate" label="成功率" width="100">
        <template slot-scope="scope">
          {{ scope.row.success_rate }}%
        </template>
      </el-table-column>
      <el-table-column label="操作" width="120">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="viewDetails(scope.row)">详情</el-button>
          <el-button type="text" size="small" @click="deleteHistory(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
export default {
  name: 'SceneExecutionHistory',
  props: {
    projectId: {
      type: Number,
      required: true
    },
    sceneId: {
      type: [Number, String],
      default: null
    }
  },
  data() {
    return {
      historyList: [],
      loading: false
    }
  },
  mounted() {
    this.loadHistory()
  },
  methods: {
    loadHistory() {
      this.loading = true
      // 模拟数据
      setTimeout(() => {
        this.historyList = [
          {
            id: 1,
            status: 'completed',
            start_time: '2024-01-15 10:30:00',
            duration: 2500,
            success_rate: 100
          },
          {
            id: 2,
            status: 'failed',
            start_time: '2024-01-15 09:15:00',
            duration: 1800,
            success_rate: 75
          }
        ]
        this.loading = false
      }, 1000)
    },
    refreshHistory() {
      this.loadHistory()
    },
    viewDetails(history) {
      this.$message.info(`查看执行历史 ${history.id} 详情功能开发中`)
    },
    deleteHistory(history) {
      this.$message.info(`删除执行历史 ${history.id} 功能开发中`)
    },
    getStatusTagType(status) {
      const typeMap = {
        'completed': 'success',
        'failed': 'danger',
        'running': 'warning'
      }
      return typeMap[status] || 'info'
    },
    getStatusText(status) {
      const textMap = {
        'completed': '已完成',
        'failed': '失败',
        'running': '运行中'
      }
      return textMap[status] || status
    }
  }
}
</script>

<style scoped>
.scene-execution-history {
  padding: 16px;
}
.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}
.history-header h4 {
  margin: 0;
  color: #333;
}
</style>
