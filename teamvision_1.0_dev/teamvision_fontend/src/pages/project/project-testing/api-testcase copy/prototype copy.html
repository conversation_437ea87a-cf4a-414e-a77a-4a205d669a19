<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试用例 - TeamVision</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: #f5f5f5;
            height: 100vh;
            overflow: hidden;
        }

        .container {
            display: flex;
            height: 100vh;
            background: white;
        }

        /* 左侧导航栏 */
        .nav-sidebar {
            width: 60px;
            background: #f8f9fa;
            border-right: 1px solid #e0e0e0;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px 0;
        }

        .nav-icon {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 6px;
            cursor: pointer;
            margin-bottom: 8px;
            color: #666;
            font-size: 18px;
            transition: all 0.2s ease;
        }

        .nav-icon:hover {
            background: #e9ecef;
            color: #333;
        }

        .nav-icon.active {
            background: #1890ff;
            color: white;
        }

        /* 左侧功能面板 */
        .left-panel {
            width: 360px;
            background: white;
            border-right: 1px solid #e0e0e0;
            display: flex;
            flex-direction: column;
        }

        .panel-tab {
            display: none;
            flex-direction: column;
            height: 100%;
        }

        .panel-tab.active {
            display: flex;
        }

        .left-header {
            padding: 16px;
            border-bottom: 1px solid #e0e0e0;
        }

        .breadcrumb {
            font-size: 12px;
            color: #666;
            margin-bottom: 12px;
        }

        .search-bar {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            margin-bottom: 12px;
        }

        .action-buttons {
            display: flex;
            gap: 8px;
            align-items: center;
            justify-content: space-between;
        }

        .new-btn {
            background: #1890ff;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .header-icons {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .icon-btn {
            width: 32px;
            height: 32px;
            border: none;
            background: transparent;
            border-radius: 4px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
        }

        .icon-btn:hover {
            background: #f0f0f0;
        }

        .collection-tree {
            flex: 1;
            padding: 8px;
            overflow-y: auto;
        }

        .tree-item {
            display: flex;
            align-items: center;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            position: relative;
        }

        .tree-item:hover {
            background: #f5f5f5;
        }

        .tree-item.folder {
            margin-left: 0;
        }

        .tree-item.request {
            margin-left: 24px;
        }

        .tree-icon {
            margin-right: 8px;
            width: 16px;
            height: 16px;
        }

        .tree-label {
            flex: 1;
            font-size: 14px;
            color: #333;
        }

        .method-badge {
            background: #52c41a;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 11px;
            font-weight: bold;
            margin-right: 6px;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #52c41a;
            margin-right: 8px;
        }

        .tree-actions {
            opacity: 0;
            transition: opacity 0.2s;
        }

        .tree-item:hover .tree-actions {
            opacity: 1;
        }

        /* 右侧主工作区 */
        .right-panel {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: white;
        }

        /* 请求标签页栏 */
        .request-tabs-bar {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            border-bottom: 1px solid #e0e0e0;
            background: white;
            height: 48px;
        }

        .request-tabs {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .request-tab {
            display: flex;
            align-items: center;
            padding: 8px 16px;
            border-radius: 6px 6px 0 0;
            cursor: pointer;
            background: #f5f5f5;
            border: 1px solid transparent;
            border-bottom: none;
            position: relative;
            min-width: 120px;
        }

        .request-tab.active {
            background: white;
            border-color: #e0e0e0;
            color: #722ed1;
            font-weight: 600;
        }

        .request-tab:hover:not(.active) {
            background: #fafafa;
        }

        .tab-method {
            background: #52c41a;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 11px;
            font-weight: bold;
            margin-right: 8px;
        }

        .tab-name {
            flex: 1;
            font-size: 14px;
        }

        .tab-close {
            margin-left: 8px;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: #666;
            opacity: 0;
            transition: opacity 0.2s;
        }

        .request-tab:hover .tab-close {
            opacity: 1;
        }

        .tab-close:hover {
            background: #ff4d4f;
            color: white;
        }

        .new-tab {
            width: 32px;
            height: 32px;
            min-width: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f0f0f0;
            color: #666;
            font-size: 18px;
            font-weight: bold;
        }

        .new-tab:hover {
            background: #e0e0e0;
            color: #333;
        }

        .tab-actions {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .env-selector {
            display: flex;
            align-items: center;
            gap: 6px;
            padding: 6px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            cursor: pointer;
            background: white;
            font-size: 14px;
        }

        .env-selector:hover {
            border-color: #1890ff;
        }

        .eye-btn {
            width: 32px;
            height: 32px;
            border: 1px solid #ddd;
            background: white;
            border-radius: 4px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
        }

        .eye-btn:hover {
            border-color: #1890ff;
            color: #1890ff;
        }

        .variables-section {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 14px;
            color: #666;
        }

        .add-var-btn {
            width: 24px;
            height: 24px;
            border: 1px solid #ddd;
            background: white;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: #666;
        }

        .add-var-btn:hover {
            border-color: #1890ff;
            color: #1890ff;
        }

        /* 请求构建区域 */
        .request-builder {
            padding: 16px;
            border-bottom: 1px solid #e0e0e0;
            background: white;
        }

        .request-header {
            display: flex;
            gap: 12px;
            align-items: center;
            margin-bottom: 12px;
        }

        .method-select {
            background: #52c41a;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
        }

        .url-input {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .env-select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .action-buttons-right {
            display: flex;
            gap: 8px;
        }

        .send-btn {
            background: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 4px;
            position: relative;
        }

        .save-btn {
            background: white;
            color: #666;
            border: 1px solid #ddd;
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 4px;
            position: relative;
        }

        .dropdown-arrow {
            margin-left: 4px;
            font-size: 10px;
            opacity: 0.7;
        }

        /* 选项卡 */
        .tabs {
            display: flex;
            border-bottom: 1px solid #e0e0e0;
            background: white;
        }

        .tab {
            padding: 12px 20px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            font-size: 14px;
            color: #666;
        }

        .tab.active {
            color: #1890ff;
            border-bottom-color: #1890ff;
        }

        .tab:hover {
            color: #1890ff;
        }

        /* 内容区域 */
        .content-area {
            flex: 1;
            display: flex;
            background: #fafafa;
        }

        .main-content {
            flex: 1;
            padding: 20px;
            background: white;
            margin: 16px;
            border-radius: 6px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .section-title {
            font-size: 14px;
            font-weight: 600;
            color: #333;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .section-actions {
            display: flex;
            gap: 8px;
        }

        .action-icon {
            width: 24px;
            height: 24px;
            border: none;
            background: transparent;
            cursor: pointer;
            color: #666;
            border-radius: 3px;
        }

        .action-icon:hover {
            background: #f0f0f0;
        }

        .params-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
        }

        .params-table th {
            background: #fafafa;
            padding: 12px;
            text-align: left;
            font-size: 12px;
            font-weight: 600;
            color: #666;
            border-bottom: 1px solid #e0e0e0;
        }

        .params-table td {
            padding: 12px;
            border-bottom: 1px solid #f0f0f0;
        }

        .params-table input {
            width: 100%;
            border: none;
            outline: none;
            font-size: 14px;
            padding: 4px;
        }

        .params-table input:focus {
            background: #f8f9fa;
        }

        .param-row {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .checkbox {
            width: 16px;
            height: 16px;
        }

        .param-actions {
            display: flex;
            gap: 4px;
        }

        .param-action {
            width: 20px;
            height: 20px;
            border: none;
            background: transparent;
            cursor: pointer;
            border-radius: 3px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .param-action:hover {
            background: #f0f0f0;
        }

        .param-action.success {
            color: #52c41a;
        }

        .param-action.success:hover {
            background: #f6ffed;
        }

        .param-action.danger {
            color: #ff4d4f;
        }

        .param-action.danger:hover {
            background: #fff2f0;
        }

        .add-param-btn {
            width: 100%;
            padding: 12px;
            border: 1px dashed #d9d9d9;
            background: white;
            color: #666;
            cursor: pointer;
            border-radius: 4px;
            margin-top: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
        }

        .add-param-btn:hover {
            border-color: #1890ff;
            color: #1890ff;
        }

        /* 右侧变量面板 */
        .variables-panel {
            width: 280px;
            background: white;
            border-left: 1px solid #e0e0e0;
            padding: 16px;
            overflow-y: auto;
        }

        .variables-title {
            font-size: 14px;
            font-weight: 600;
            color: #333;
            margin-bottom: 12px;
        }

        .variable-item {
            display: flex;
            align-items: center;
            padding: 8px;
            border-radius: 4px;
            cursor: pointer;
            margin-bottom: 4px;
        }

        .variable-item:hover {
            background: #f5f5f5;
        }

        .variable-key {
            font-weight: 600;
            color: #1890ff;
            margin-right: 8px;
        }

        .variable-value {
            color: #666;
            font-size: 12px;
        }

        .insert-btn {
            margin-left: auto;
            padding: 4px 8px;
            background: #1890ff;
            color: white;
            border: none;
            border-radius: 3px;
            font-size: 12px;
            cursor: pointer;
        }

        /* Environments 特定样式 */
        .environment-header {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
            padding: 8px 12px;
            background: #f8f9fa;
            border-radius: 6px;
        }

        .globe-icon {
            margin-right: 8px;
            font-size: 16px;
        }

        .header-title {
            flex: 1;
            font-size: 14px;
            font-weight: 600;
            color: #333;
        }

        .empty-state {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 40px 20px;
            text-align: center;
        }

        .empty-icon {
            margin-bottom: 20px;
        }

        .cube-icon {
            font-size: 64px;
            opacity: 0.6;
        }

        .empty-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }

        .empty-subtitle {
            font-size: 14px;
            color: #666;
            margin-bottom: 24px;
        }

        .empty-actions {
            display: flex;
            gap: 12px;
        }

        .import-btn {
            background: #722ed1;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .add-btn {
            background: white;
            color: #666;
            border: 1px solid #ddd;
            padding: 12px 20px;
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        /* Code snippet 特定样式 */
        .language-selector {
            margin-bottom: 16px;
        }

        .language-selector select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .code-section {
            flex: 1;
            padding: 16px;
        }

        .code-editor {
            background: #f8f9fa;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 13px;
            line-height: 1.5;
            overflow: hidden;
        }

        .code-line {
            display: flex;
            padding: 8px 12px;
            border-bottom: 1px solid #f0f0f0;
        }

        .code-line:last-child {
            border-bottom: none;
        }

        .line-number {
            width: 30px;
            color: #999;
            font-size: 12px;
            text-align: right;
            margin-right: 12px;
            user-select: none;
        }

        .code-content {
            flex: 1;
        }

        .keyword {
            color: #d73a49;
            font-weight: bold;
        }

        .flag {
            color: #005cc5;
        }

        .method {
            color: #6f42c1;
            font-weight: bold;
        }

        .url {
            color: #032f62;
        }

        /* 底部帮助区域 */
        .help-overlay {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 1000;
        }

        .help-content {
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 16px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .help-items {
            display: flex;
            gap: 20px;
        }

        .help-item {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 12px;
            color: #666;
        }

        .help-text {
            font-weight: 500;
        }

        .help-shortcut {
            background: #f0f0f0;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 11px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            color: #333;
            border: 1px solid #d9d9d9;
        }

        .doc-btn {
            background: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 12px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 6px;
            font-weight: 500;
            transition: background 0.2s;
        }

        .doc-btn:hover {
            background: #40a9ff;
        }

        /* 环境弹窗 */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.5);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 2000;
        }

        .modal-overlay.show {
            display: flex;
        }

        .modal {
            background: white;
            border-radius: 8px;
            width: 600px;
            max-height: 80vh;
            overflow: hidden;
            box-shadow: 0 8px 24px rgba(0,0,0,0.2);
        }

        .modal-header {
            padding: 16px 20px;
            border-bottom: 1px solid #e0e0e0;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .modal-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }

        .modal-close {
            width: 32px;
            height: 32px;
            border: none;
            background: transparent;
            cursor: pointer;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
        }

        .modal-close:hover {
            background: #f0f0f0;
        }

        .modal-body {
            padding: 20px;
        }

        .form-group {
            margin-bottom: 16px;
        }

        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }

        .form-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .form-input:focus {
            outline: none;
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24,144,255,0.2);
        }

        .modal-tabs {
            display: flex;
            border-bottom: 1px solid #e0e0e0;
            margin-bottom: 16px;
        }

        .modal-tab {
            padding: 12px 20px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            font-size: 14px;
            color: #666;
        }

        .modal-tab.active {
            color: #1890ff;
            border-bottom-color: #1890ff;
        }

        .modal-tab:hover {
            color: #1890ff;
        }

        .modal-footer {
            padding: 16px 20px;
            border-top: 1px solid #e0e0e0;
            display: flex;
            justify-content: flex-end;
            gap: 12px;
        }

        .btn {
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
            border: 1px solid transparent;
        }

        .btn-primary {
            background: #1890ff;
            color: white;
            border-color: #1890ff;
        }

        .btn-secondary {
            background: white;
            color: #666;
            border-color: #ddd;
        }

        .btn:hover {
            opacity: 0.8;
        }

        /* 响应区域 */
        .response-area {
            margin-top: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            background: white;
        }

        .response-header {
            padding: 12px 16px;
            background: #fafafa;
            border-bottom: 1px solid #e0e0e0;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .status-code {
            padding: 4px 8px;
            border-radius: 3px;
            font-size: 12px;
            font-weight: bold;
            color: white;
        }

        .status-200 {
            background: #52c41a;
        }

        .response-meta {
            font-size: 12px;
            color: #666;
        }

        .response-body {
            padding: 16px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 13px;
            line-height: 1.5;
            background: #f8f9fa;
            border-radius: 0 0 6px 6px;
            max-height: 300px;
            overflow-y: auto;
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .left-panel {
                width: 300px;
            }
            
            .variables-panel {
                width: 240px;
            }
        }

        @media (max-width: 768px) {
            .container {
                flex-direction: column;
            }
            
            .nav-sidebar {
                width: 100%;
                height: 60px;
                flex-direction: row;
                padding: 10px 20px;
            }
            
            .nav-icon {
                margin-bottom: 0;
                margin-right: 8px;
            }
            
            .left-panel {
                width: 100%;
                height: 200px;
            }
            
            .variables-panel {
                display: none;
            }
        }

        /* 动画效果 */
        .fade-in {
            animation: fadeIn 0.3s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .slide-in {
            animation: slideIn 0.3s ease-out;
        }

        @keyframes slideIn {
            from { transform: translateX(-20px); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        /* Scene 特定样式 */
        .scene-section {
            margin-bottom: 16px;
        }

        .scene-header {
            display: flex;
            align-items: center;
            padding: 8px 12px;
            background: #f8f9fa;
            border-radius: 6px;
            margin-bottom: 8px;
        }

        .scene-icon {
            margin-right: 8px;
            font-size: 16px;
            color: #722ed1;
        }

        .scene-title {
            flex: 1;
            font-size: 14px;
            font-weight: 600;
            color: #333;
        }

        .scene-count {
            font-size: 12px;
            color: #666;
            margin-right: 8px;
        }

        .scene-actions {
            display: flex;
            gap: 4px;
        }

        .scene-subsection {
            margin-left: 16px;
            padding-left: 12px;
            border-left: 2px solid #f0f0f0;
        }

        .subsection-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 6px 0;
            margin-bottom: 8px;
        }

        .subsection-title {
            font-size: 13px;
            color: #666;
            font-weight: 500;
        }

        .subsection-count {
            background: #f0f0f0;
            color: #666;
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 11px;
            min-width: 16px;
            text-align: center;
        }

        .scene-item {
            margin-left: 0;
            padding: 6px 12px;
            background: #fafafa;
            border-radius: 4px;
            margin-bottom: 4px;
        }

        .scene-item:hover {
            background: #f0f0f0;
        }

        .scene-item-name {
            flex: 1;
            font-size: 14px;
            color: #333;
        }

        .import-btn {
            background: white !important;
            color: #666 !important;
            border: 1px solid #ddd !important;
            padding: 6px 12px;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .import-btn:hover {
            background: #f5f5f5 !important;
            border-color: #1890ff !important;
            color: #1890ff !important;
        }

        /* 添加场景选中状态样式 */
        .scene-item.selected {
            background: #e6f7ff !important;
            border-left: 3px solid #1890ff;
        }

        .scene-section.collapsed .scene-subsection {
            display: none;
        }

        .collapse-btn {
            transition: transform 0.2s ease;
        }

        .scene-section.collapsed .collapse-btn {
            transform: rotate(-90deg);
        }

        .scene-header {
            cursor: pointer;
            user-select: none;
        }

        .scene-header:hover {
            background: #f0f0f0;
        }

        .search-bar:focus {
            outline: none;
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24,144,255,0.2);
        }

        .loading-state {
            opacity: 0.6;
            pointer-events: none;
        }

        .loading-state::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 20px;
            height: 20px;
            margin: -10px 0 0 -10px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #1890ff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 左侧导航栏 -->
        <div class="nav-sidebar">
            <div class="nav-icon active" onclick="switchNavTab('collections')" title="Collections">
                <span>📁</span>
            </div>
            <div class="nav-icon" onclick="switchNavTab('environments')" title="Environments">
                <span>📋</span>
            </div>
            <div class="nav-icon" onclick="switchNavTab('history')" title="History">
                <span>🕒</span>
            </div>
            <div class="nav-icon" onclick="switchNavTab('code')" title="Code">
                <span>&lt;/&gt;</span>
            </div>
            <div class="nav-icon" onclick="switchNavTab('scenes')" title="Scenes">
                <span>场景</span>
            </div>
        </div>

        <!-- 左侧功能面板 -->
        <div class="left-panel">
            <!-- Collections 选项卡 -->
            <div id="collections-tab" class="panel-tab active">
                <div class="left-header">
                    <div class="breadcrumb">
                        Personal Workspace > Collections
                    </div>
                    <input type="text" class="search-bar" placeholder="Search">
                    <div class="action-buttons">
                        <button class="new-btn" onclick="showNewCollectionDialog()">
                            <span>+</span> New
                        </button>
                        <div class="header-icons">
                            <button class="icon-btn" title="Help">
                                <span>?</span>
                            </button>
                            <button class="icon-btn" title="Download">
                                <span>↓</span>
                            </button>
                            <button class="icon-btn" title="More">
                                <span>⋯</span>
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="collection-tree">
                    <div class="tree-item folder">
                        <span class="tree-icon">📁</span>
                        <span class="tree-label">login</span>
                        <div class="tree-actions">
                            <span>⋯</span>
                        </div>
                    </div>
                    <div class="tree-item request">
                        <span class="method-badge">GET</span>
                        <span class="tree-label">login</span>
                        <span class="status-dot"></span>
                        <div class="tree-actions">
                            <span>⋯</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Environments 选项卡 -->
            <div id="environments-tab" class="panel-tab">
                <div class="left-header">
                    <div class="breadcrumb">
                        Personal Workspace > Environments
                    </div>
                    <div class="environment-header">
                        <span class="globe-icon">🌐</span>
                        <span class="header-title">Global</span>
                        <div class="tree-actions">
                            <span>⋯</span>
                        </div>
                    </div>
                    <input type="text" class="search-bar" placeholder="Search">
                    <div class="action-buttons">
                        <button class="new-btn" onclick="showNewEnvironmentDialog()">
                            <span>+</span> New
                        </button>
                        <div class="header-icons">
                            <button class="icon-btn" title="Help">
                                <span>?</span>
                            </button>
                            <button class="icon-btn" title="Folders">
                                <span>📁</span>
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="empty-state">
                    <div class="empty-icon">
                        <div class="cube-icon">🎯</div>
                    </div>
                    <div class="empty-title">Environments are empty</div>
                    <div class="empty-subtitle">Import or create an environment</div>
                    <div class="empty-actions">
                        <button class="import-btn" onclick="importEnvironment()">
                            <span>📥</span> Import
                        </button>
                        <button class="add-btn" onclick="showNewEnvironmentDialog()">
                            <span>+</span> Add new
                        </button>
                    </div>
                </div>
            </div>

            <!-- Code snippet 选项卡 -->
            <div id="codesnippet-tab" class="panel-tab">
                <div class="left-header">
                    <div class="breadcrumb">
                        Request > Code snippet
                    </div>
                    <div class="language-selector">
                        <select class="form-input">
                            <option>Shell - cURL</option>
                            <option>JavaScript - Fetch</option>
                            <option>Python - Requests</option>
                            <option>PHP - cURL</option>
                            <option>Go - HTTP</option>
                        </select>
                    </div>
                </div>
                
                <div class="code-section">
                    <div class="section-title">
                        Generated code
                        <div class="section-actions">
                            <button class="action-icon" title="Format">📋</button>
                            <button class="action-icon" title="Download">↓</button>
                            <button class="action-icon" title="Copy">📄</button>
                        </div>
                    </div>
                    <div class="code-editor">
                        <div class="code-line">
                            <span class="line-number">1</span>
                            <span class="code-content">
                                <span class="keyword">curl</span> <span class="flag">--request</span> <span class="method">GET</span> \
                            </span>
                        </div>
                        <div class="code-line">
                            <span class="line-number">2</span>
                            <span class="code-content">
                                <span class="flag">--url</span> <span class="url">https://echo.hoppscotch.io/login</span>
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Test Scene 选项卡 -->
            <div id="scenes-tab" class="panel-tab">
                <div class="left-header">
                    <div class="breadcrumb">
                        Personal Workspace > Test Scene
                    </div>
                    <input type="text" class="search-bar" placeholder="请输入场景名称进行搜索" oninput="searchScenes(this.value)">
                    <div class="action-buttons">
                        <button class="new-btn" onclick="showNewSceneDialog()">
                            <span>+</span> 新建场景
                        </button>
                        <button class="import-btn" onclick="importScene()">
                            导入场景
                        </button>
                    </div>
                </div>
                
                <div class="collection-tree">
                    <!-- 全部场景标题 -->
                    <div class="scene-section">
                        <div class="scene-header" onclick="toggleSceneSection(this)">
                            <span class="scene-icon">📁</span>
                            <span class="scene-title">全部场景</span>
                            <span class="scene-count">(2)</span>
                            <div class="scene-actions">
                                <button class="icon-btn collapse-btn" title="展开/收起">
                                    <span>−</span>
                                </button>
                                <button class="icon-btn" title="更多操作" onclick="event.stopPropagation(); showSceneMenu(this)">
                                    <span>⋯</span>
                                </button>
                            </div>
                        </div>
                        
                        <!-- 未规划场景 -->
                        <div class="scene-subsection">
                            <div class="subsection-header">
                                <span class="subsection-title">未规划场景</span>
                                <span class="subsection-count">2</span>
                            </div>
                            
                            <!-- demo场景条目 -->
                            <div class="tree-item scene-item" onclick="selectScene(this, 'demo')">
                                <span class="scene-item-name">demo</span>
                                <div class="tree-actions">
                                    <button class="action-icon" title="编辑" onclick="event.stopPropagation(); editScene('demo')">✏</button>
                                    <button class="action-icon" title="删除" onclick="event.stopPropagation(); deleteScene('demo')">🗑</button>
                                    <button class="action-icon" title="更多" onclick="event.stopPropagation(); showSceneItemMenu(this, 'demo')">⋯</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 右侧主工作区 -->
        <div class="right-panel">
            <!-- 请求标签页栏 -->
            <div class="request-tabs-bar">
                <div class="request-tabs">
                    <div class="request-tab active" data-request-id="login">
                        <span class="tab-method">GET</span>
                        <span class="tab-name">login</span>
                        <span class="tab-close" onclick="closeRequestTab('login')">×</span>
                    </div>
                    <div class="request-tab new-tab" onclick="createNewRequest()">
                        <span>+</span>
                    </div>
                </div>
                <div class="tab-actions">
                    <div class="env-selector" onclick="showEnvironmentModal()">
                        <span>📋</span>
                        <span>Select environment</span>
                        <span>▼</span>
                    </div>
                    <button class="eye-btn" title="View environment details">
                        <span>👁</span>
                    </button>
                    <div class="variables-section">
                        <span>Variables</span>
                        <button class="add-var-btn" onclick="addVariable()">
                            <span>+</span>
                        </button>
                    </div>
                </div>
            </div>

            <!-- 请求构建区域 -->
            <div class="request-builder">
                <div class="request-header">
                    <select class="method-select">
                        <option value="GET">GET</option>
                        <option value="POST">POST</option>
                        <option value="PUT">PUT</option>
                        <option value="DELETE">DELETE</option>
                    </select>
                    <input type="text" class="url-input" value="https://echo.hoppscotch.io/login" placeholder="Enter request URL">
                    <div class="action-buttons-right">
                        <button class="send-btn" onclick="sendRequest()">
                            <span>▶</span> Send
                            <span class="dropdown-arrow">▼</span>
                        </button>
                        <button class="save-btn">
                            <span>💾</span> Save
                            <span class="dropdown-arrow">▼</span>
                        </button>
                    </div>
                </div>
            </div>

            <!-- 选项卡 -->
            <div class="tabs">
                <div class="tab active" onclick="switchTab('parameters')">Parameters</div>
                <div class="tab" onclick="switchTab('body')">Body</div>
                <div class="tab" onclick="switchTab('headers')">Headers</div>
                <div class="tab" onclick="switchTab('authorization')">Authorization</div>
                <div class="tab" onclick="switchTab('pre-script')">Pre-request Script</div>
                <div class="tab" onclick="switchTab('post-script')">Post-request Script</div>
            </div>

            <!-- 内容区域 -->
            <div class="content-area">
                <div class="main-content">
                    <!-- Parameters 内容 -->
                    <div id="parameters-content" class="tab-content">
                        <div class="section-title">
                            Query Parameters
                            <div class="section-actions">
                                <button class="action-icon" title="Help">?</button>
                                <button class="action-icon" title="Delete all">🗑</button>
                                <button class="action-icon" title="Edit">✏</button>
                                <button class="action-icon" title="Add">+</button>
                            </div>
                        </div>
                        <table class="params-table">
                            <thead>
                                <tr>
                                    <th style="width: 40px;"></th>
                                    <th>Key</th>
                                    <th>Value</th>
                                    <th>Description</th>
                                    <th style="width: 80px;">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>
                                        <input type="checkbox" class="checkbox" checked>
                                    </td>
                                    <td><input type="text" placeholder="Parameter name"></td>
                                    <td><input type="text" placeholder="Parameter value"></td>
                                    <td><input type="text" placeholder="Description"></td>
                                    <td>
                                        <div class="param-actions">
                                            <button class="param-action success" title="Enable/Disable">✓</button>
                                            <button class="param-action danger" title="Delete">🗑</button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                        <button class="add-param-btn" onclick="addParameter()">
                            <span>+</span> Add Parameter
                        </button>
                    </div>

                    <!-- Body 内容 -->
                    <div id="body-content" class="tab-content" style="display: none;">
                        <div class="section-title">
                            Request Body
                            <div class="section-actions">
                                <select style="padding: 4px 8px; border: 1px solid #ddd; border-radius: 3px;">
                                    <option>form-data</option>
                                    <option>x-www-form-urlencoded</option>
                                    <option>raw</option>
                                    <option>binary</option>
                                </select>
                            </div>
                        </div>
                        <textarea style="width: 100%; height: 200px; padding: 12px; border: 1px solid #ddd; border-radius: 4px; font-family: monospace; font-size: 14px;" placeholder="Enter request body (JSON, XML, etc.)"></textarea>
                    </div>

                    <!-- Headers 内容 -->
                    <div id="headers-content" class="tab-content" style="display: none;">
                        <div class="section-title">
                            Request Headers
                            <div class="section-actions">
                                <button class="action-icon" title="Help">?</button>
                                <button class="action-icon" title="Delete all">🗑</button>
                                <button class="action-icon" title="Add">+</button>
                            </div>
                        </div>
                        <table class="params-table">
                            <thead>
                                <tr>
                                    <th style="width: 40px;"></th>
                                    <th>Key</th>
                                    <th>Value</th>
                                    <th>Description</th>
                                    <th style="width: 60px;">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>
                                        <input type="checkbox" class="checkbox" checked>
                                    </td>
                                    <td><input type="text" value="Content-Type"></td>
                                    <td><input type="text" value="application/json"></td>
                                    <td><input type="text" placeholder="Description"></td>
                                    <td>
                                        <div class="param-actions">
                                            <button class="param-action" title="Copy">✓</button>
                                            <button class="param-action" title="Delete" onclick="deleteRow(this)">🗑</button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                        <button class="add-param-btn" onclick="addHeader()">
                            <span>+</span> Add Header
                        </button>
                    </div>

                    <!-- Authorization 内容 -->
                    <div id="authorization-content" class="tab-content" style="display: none;">
                        <div class="section-title">
                            Authorization
                        </div>
                        <div class="form-group">
                            <label class="form-label">Type</label>
                            <select class="form-input">
                                <option>No Auth</option>
                                <option>Basic Auth</option>
                                <option>Bearer Token</option>
                                <option>API Key</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Username</label>
                            <input type="text" class="form-input" placeholder="Enter username">
                        </div>
                        <div class="form-group">
                            <label class="form-label">Password</label>
                            <input type="password" class="form-input" placeholder="Enter password">
                        </div>
                    </div>

                    <!-- Pre-request Script 内容 -->
                    <div id="pre-script-content" class="tab-content" style="display: none;">
                        <div class="section-title">
                            Pre-request Script
                        </div>
                        <textarea style="width: 100%; height: 200px; padding: 12px; border: 1px solid #ddd; border-radius: 4px; font-family: monospace; font-size: 14px;" placeholder="// Write your pre-request script here&#10;// Example:&#10;// pm.environment.set('timestamp', Date.now());"></textarea>
                    </div>

                    <!-- Post-request Script 内容 -->
                    <div id="post-script-content" class="tab-content" style="display: none;">
                        <div class="section-title">
                            Post-request Script
                        </div>
                        <textarea style="width: 100%; height: 200px; padding: 12px; border: 1px solid #ddd; border-radius: 4px; font-family: monospace; font-size: 14px;" placeholder="// Write your post-request script here&#10;// Example:&#10;// pm.test('Status code is 200', function () {&#10;//     pm.response.to.have.status(200);&#10;// });"></textarea>
                    </div>

                    <!-- 响应区域 -->
                    <div id="response-area" class="response-area" style="display: none;">
                        <div class="response-header">
                            <span class="status-code status-200">200 OK</span>
                            <span class="response-meta">Time: 245ms | Size: 1.2KB</span>
                        </div>
                        <div class="response-body">
{
  "success": true,
  "message": "Login successful",
  "data": {
    "user": {
      "id": 12345,
      "username": "zhang",
      "email": "<EMAIL>"
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  }
}
                        </div>
                    </div>
                </div>


            </div>
        </div>
    </div>

    <!-- 环境管理弹窗 -->
    <div class="modal-overlay" id="environmentModal">
        <div class="modal">
            <div class="modal-header">
                <div class="modal-title">New Environment</div>
                <button class="modal-close" onclick="hideEnvironmentModal()">×</button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label class="form-label">Label</label>
                    <input type="text" class="form-input" placeholder="Environment name (e.g., dev, test, prod)">
                </div>
                
                <div class="modal-tabs">
                    <div class="modal-tab active" onclick="switchModalTab('variables')">Variables</div>
                    <div class="modal-tab" onclick="switchModalTab('secrets')">Secrets</div>
                </div>

                <!-- Variables 页签 -->
                <div id="variables-tab" class="modal-tab-content">
                    <div class="section-title">
                        Variables
                        <div class="section-actions">
                            <button class="action-icon" title="Add">+</button>
                            <button class="action-icon" title="Paste">📋</button>
                        </div>
                    </div>
                    <table class="params-table">
                        <thead>
                            <tr>
                                <th>Key</th>
                                <th>Value</th>
                                <th>Description</th>
                                <th style="width: 60px;">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><input type="text" value="user"></td>
                                <td><input type="text" value="zhang"></td>
                                <td><input type="text" placeholder="Description"></td>
                                <td>
                                    <div class="param-actions">
                                        <button class="param-action" title="Copy">📋</button>
                                        <button class="param-action" title="Delete">🗑</button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td><input type="text" value="password"></td>
                                <td><input type="text" value="123456"></td>
                                <td><input type="text" placeholder="Description"></td>
                                <td>
                                    <div class="param-actions">
                                        <button class="param-action" title="Copy">📋</button>
                                        <button class="param-action" title="Delete">🗑</button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    <button class="add-param-btn" onclick="addEnvironmentVariable()">
                        <span>+</span> Add Variable
                    </button>
                </div>

                <!-- Secrets 页签 -->
                <div id="secrets-tab" class="modal-tab-content" style="display: none;">
                    <div class="section-title">
                        Secrets
                        <div class="section-actions">
                            <button class="action-icon" title="Add">+</button>
                            <button class="action-icon" title="Paste">📋</button>
                        </div>
                    </div>
                    <table class="params-table">
                        <thead>
                            <tr>
                                <th>Key</th>
                                <th>Value</th>
                                <th>Description</th>
                                <th style="width: 60px;">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><input type="text" value="apiKey"></td>
                                <td><input type="password" value="••••••••••••"></td>
                                <td><input type="text" placeholder="Description"></td>
                                <td>
                                    <div class="param-actions">
                                        <button class="param-action" title="Copy">📋</button>
                                        <button class="param-action" title="Delete">🗑</button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    <button class="add-param-btn" onclick="addEnvironmentSecret()">
                        <span>+</span> Add Secret
                    </button>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="hideEnvironmentModal()">Cancel</button>
                <button class="btn btn-primary" onclick="saveEnvironment()">Save</button>
            </div>
        </div>
    </div>

    <script>
        // 导航选项卡切换
        function switchNavTab(tabName) {
            // 移除所有导航图标的活动状态
            document.querySelectorAll('.nav-icon').forEach(icon => icon.classList.remove('active'));
            
            // 移除所有面板选项卡的活动状态
            document.querySelectorAll('.panel-tab').forEach(tab => tab.classList.remove('active'));
            
            // 激活当前导航图标
            event.target.classList.add('active');
            
            // 激活对应的面板选项卡
            let tabId;
            switch(tabName) {
                case 'code':
                    tabId = 'codesnippet-tab';
                    break;
                case 'scenes':
                    tabId = 'scenes-tab';
                    break;
                default:
                    tabId = tabName + '-tab';
            }
            
            const targetTab = document.getElementById(tabId);
            if (targetTab) {
                targetTab.classList.add('active');
            }
        }

        // 请求配置选项卡切换
        function switchTab(tabName) {
            // 移除所有活动状态
            document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
            document.querySelectorAll('.tab-content').forEach(content => content.style.display = 'none');
            
            // 激活当前选项卡
            event.target.classList.add('active');
            document.getElementById(tabName + '-content').style.display = 'block';
        }

        // 环境弹窗显示/隐藏
        function showEnvironmentModal() {
            document.getElementById('environmentModal').classList.add('show');
        }

        function hideEnvironmentModal() {
            document.getElementById('environmentModal').classList.remove('show');
        }

        // 环境弹窗选项卡切换
        function switchModalTab(tabName) {
            document.querySelectorAll('.modal-tab').forEach(tab => tab.classList.remove('active'));
            document.querySelectorAll('.modal-tab-content').forEach(content => content.style.display = 'none');
            
            event.target.classList.add('active');
            document.getElementById(tabName + '-tab').style.display = 'block';
        }

        // 发送请求
        function sendRequest() {
            const sendBtn = event.target;
            const originalText = sendBtn.innerHTML;
            
            // 显示加载状态
            sendBtn.innerHTML = '<span>⏳</span> Sending...';
            sendBtn.disabled = true;
            
            // 模拟请求延迟
            setTimeout(() => {
                // 显示响应区域
                document.getElementById('response-area').style.display = 'block';
                
                // 恢复按钮状态
                sendBtn.innerHTML = originalText;
                sendBtn.disabled = false;
                
                // 添加动画效果
                document.getElementById('response-area').classList.add('fade-in');
            }, 1500);
        }

        // 添加参数
        function addParameter() {
            const table = document.querySelector('.params-table tbody');
            const newRow = table.insertRow();
            newRow.innerHTML = `
                <td><input type="checkbox" class="checkbox" checked></td>
                <td><input type="text" placeholder="Parameter name"></td>
                <td><input type="text" placeholder="Parameter value"></td>
                <td><input type="text" placeholder="Description"></td>
                <td>
                    <div class="param-actions">
                        <button class="param-action" title="Copy">✓</button>
                        <button class="param-action" title="Delete" onclick="deleteRow(this)">🗑</button>
                    </div>
                </td>
            `;
            newRow.classList.add('slide-in');
        }

        // 添加头部
        function addHeader() {
            const table = document.querySelector('#headers-content .params-table tbody');
            const newRow = table.insertRow();
            newRow.innerHTML = `
                <td><input type="checkbox" class="checkbox" checked></td>
                <td><input type="text" placeholder="Header name"></td>
                <td><input type="text" placeholder="Header value"></td>
                <td><input type="text" placeholder="Description"></td>
                <td>
                    <div class="param-actions">
                        <button class="param-action" title="Copy">✓</button>
                        <button class="param-action" title="Delete" onclick="deleteRow(this)">🗑</button>
                    </div>
                </td>
            `;
            newRow.classList.add('slide-in');
        }

        // 删除行
        function deleteRow(button) {
            button.closest('tr').remove();
        }

        // 添加环境变量
        function addEnvironmentVariable() {
            const table = document.querySelector('#variables-tab .params-table tbody');
            const newRow = table.insertRow();
            newRow.innerHTML = `
                <td><input type="text" placeholder="Variable name"></td>
                <td><input type="text" placeholder="Variable value"></td>
                <td><input type="text" placeholder="Description"></td>
                <td>
                    <div class="param-actions">
                        <button class="param-action" title="Copy">📋</button>
                        <button class="param-action" title="Delete" onclick="deleteRow(this)">🗑</button>
                    </div>
                </td>
            `;
            newRow.classList.add('slide-in');
        }

        // 添加环境密钥
        function addEnvironmentSecret() {
            const table = document.querySelector('#secrets-tab .params-table tbody');
            const newRow = table.insertRow();
            newRow.innerHTML = `
                <td><input type="text" placeholder="Secret name"></td>
                <td><input type="password" placeholder="Secret value"></td>
                <td><input type="text" placeholder="Description"></td>
                <td>
                    <div class="param-actions">
                        <button class="param-action" title="Copy">📋</button>
                        <button class="param-action" title="Delete" onclick="deleteRow(this)">🗑</button>
                    </div>
                </td>
            `;
            newRow.classList.add('slide-in');
        }

        // 插入变量
        function insertVariable(varName) {
            const activeInput = document.activeElement;
            if (activeInput && activeInput.tagName === 'INPUT') {
                activeInput.value += `{{${varName}}}`;
            } else {
                // 如果没有活动的输入框，插入到URL输入框
                const urlInput = document.querySelector('.url-input');
                urlInput.focus();
                urlInput.value += `{{${varName}}}`;
            }
        }

        // 保存环境
        function saveEnvironment() {
            const label = document.querySelector('#environmentModal .form-input').value;
            if (!label.trim()) {
                alert('请输入环境名称');
                return;
            }
            
            // 这里应该调用实际的保存逻辑
            console.log('保存环境:', label);
            hideEnvironmentModal();
        }

        // 显示新建集合对话框
        function showNewCollectionDialog() {
            const name = prompt('请输入集合名称:');
            if (name && name.trim()) {
                // 这里应该调用实际的创建逻辑
                console.log('创建集合:', name);
            }
        }

        // 显示新建环境对话框
        function showNewEnvironmentDialog() {
            showEnvironmentModal();
        }

        // 导入环境
        function importEnvironment() {
            // 创建文件输入元素
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.json';
            input.onchange = function(e) {
                const file = e.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        try {
                            const data = JSON.parse(e.target.result);
                            console.log('导入环境数据:', data);
                            alert('环境导入成功！');
                        } catch (error) {
                            alert('文件格式错误，请选择有效的JSON文件');
                        }
                    };
                    reader.readAsText(file);
                }
            };
            input.click();
        }

        // 创建新请求标签页
        function createNewRequest() {
            const requestId = 'request_' + Date.now();
            const tabsContainer = document.querySelector('.request-tabs');
            const newTab = tabsContainer.insertBefore(document.createElement('div'), document.querySelector('.new-tab'));
            
            newTab.className = 'request-tab active';
            newTab.setAttribute('data-request-id', requestId);
            newTab.innerHTML = `
                <span class="tab-method">GET</span>
                <span class="tab-name">New Request</span>
                <span class="tab-close" onclick="closeRequestTab('${requestId}')">×</span>
            `;
            
            // 移除其他标签页的活动状态
            document.querySelectorAll('.request-tab:not(.new-tab)').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // 清空当前请求配置
            document.querySelector('.method-select').value = 'GET';
            document.querySelector('.url-input').value = '';
            
            console.log('创建新请求:', requestId);
        }

        // 关闭请求标签页
        function closeRequestTab(requestId) {
            const tab = document.querySelector(`[data-request-id="${requestId}"]`);
            if (tab) {
                tab.remove();
                
                // 如果关闭的是当前活动标签页，激活其他标签页
                if (tab.classList.contains('active')) {
                    const remainingTabs = document.querySelectorAll('.request-tab:not(.new-tab)');
                    if (remainingTabs.length > 0) {
                        remainingTabs[remainingTabs.length - 1].classList.add('active');
                    }
                }
            }
        }

        // 添加变量
        function addVariable() {
            const name = prompt('请输入变量名:');
            if (name && name.trim()) {
                const value = prompt('请输入变量值:');
                if (value !== null) {
                    console.log('添加变量:', name, value);
                    // 这里应该调用实际的添加变量逻辑
                }
            }
        }

        // 生成代码片段
        function generateCodeSnippet() {
            const method = document.querySelector('.method-select').value;
            const url = document.querySelector('.url-input').value;
            const language = document.querySelector('#codesnippet-tab select').value;
            
            let code = '';
            
            switch (language) {
                case 'Shell - cURL':
                    code = `curl --request ${method} \\\n--url ${url}`;
                    break;
                case 'JavaScript - Fetch':
                    code = `fetch('${url}', {\n  method: '${method}',\n  headers: {\n    'Content-Type': 'application/json'\n  }\n})\n.then(response => response.json())\n.then(data => console.log(data));`;
                    break;
                case 'Python - Requests':
                    code = `import requests\n\nresponse = requests.${method.toLowerCase()}('${url}')\nprint(response.json())`;
                    break;
                default:
                    code = `# ${language} code for ${method} ${url}`;
            }
            
            // 更新代码编辑器内容
            const codeEditor = document.querySelector('.code-editor');
            const lines = code.split('\n');
            codeEditor.innerHTML = lines.map((line, index) => `
                <div class="code-line">
                    <span class="line-number">${index + 1}</span>
                    <span class="code-content">${line.replace(/curl|--request|GET|POST|PUT|DELETE|--url/g, match => {
                        if (match === 'curl') return '<span class="keyword">curl</span>';
                        if (match.startsWith('--')) return '<span class="flag">' + match + '</span>';
                        if (['GET', 'POST', 'PUT', 'DELETE'].includes(match)) return '<span class="method">' + match + '</span>';
                        return match;
                    })}</span>
                </div>
            `).join('');
        }

        // 键盘快捷键
        document.addEventListener('keydown', function(e) {
            // Cmd/Ctrl + Enter 发送请求
            if ((e.metaKey || e.ctrlKey) && e.key === 'Enter') {
                e.preventDefault();
                sendRequest();
            }
            
            // Cmd/Ctrl + / 显示快捷键帮助
            if ((e.metaKey || e.ctrlKey) && e.key === '/') {
                e.preventDefault();
                alert('快捷键帮助:\n⌘⏎ 发送请求\n⌘/ 显示帮助\n⌘K 搜索菜单');
            }
            
            // Cmd/Ctrl + K 搜索菜单
            if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
                e.preventDefault();
                document.querySelector('.search-bar').focus();
            }
        });

        // 点击模态框外部关闭
        document.getElementById('environmentModal').addEventListener('click', function(e) {
            if (e.target === this) {
                hideEnvironmentModal();
            }
        });

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 设置默认环境选择
            const envSelect = document.querySelector('.env-select');
            envSelect.innerHTML = '<span>📋</span><span>dev</span><span>▼</span>';
            
            // 添加一些交互效果
            document.querySelectorAll('.tree-item').forEach(item => {
                item.addEventListener('click', function() {
                    document.querySelectorAll('.tree-item').forEach(i => i.classList.remove('selected'));
                    this.classList.add('selected');
                });
            });

            // 监听语言选择器变化，自动生成代码片段
            const languageSelector = document.querySelector('#codesnippet-tab select');
            if (languageSelector) {
                languageSelector.addEventListener('change', generateCodeSnippet);
            }

            // 监听请求方法或URL变化，自动生成代码片段
            const methodSelect = document.querySelector('.method-select');
            const urlInput = document.querySelector('.url-input');
            
            if (methodSelect) {
                methodSelect.addEventListener('change', generateCodeSnippet);
            }
            
            if (urlInput) {
                urlInput.addEventListener('input', generateCodeSnippet);
            }

            // 初始化代码片段
            generateCodeSnippet();
        });

        // 显示新建场景对话框
        function showNewSceneDialog() {
            const name = prompt('请输入场景名称:');
            if (name && name.trim()) {
                console.log('创建场景:', name);
                // 这里应该调用实际的创建场景逻辑
                addSceneToList(name);
            }
        }

        // 导入场景
        function importScene() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.json';
            input.onchange = function(e) {
                const file = e.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        try {
                            const data = JSON.parse(e.target.result);
                            console.log('导入场景数据:', data);
                            alert('场景导入成功！');
                        } catch (error) {
                            alert('文件格式错误，请选择有效的JSON文件');
                        }
                    };
                    reader.readAsText(file);
                }
            };
            input.click();
        }

        // 场景搜索功能
        function searchScenes(searchText) {
            const sceneItems = document.querySelectorAll('.scene-item');
            const searchLower = searchText.toLowerCase();
            
            sceneItems.forEach(item => {
                const sceneName = item.querySelector('.scene-item-name').textContent.toLowerCase();
                if (sceneName.includes(searchLower)) {
                    item.style.display = 'flex';
                } else {
                    item.style.display = 'none';
                }
            });
        }

        // 展开/收起场景分组
        function toggleSceneSection(header) {
            const section = header.closest('.scene-section');
            const subsection = section.querySelector('.scene-subsection');
            const collapseBtn = header.querySelector('.collapse-btn span');
            
            if (subsection.style.display === 'none') {
                subsection.style.display = 'block';
                collapseBtn.textContent = '−';
                section.classList.remove('collapsed');
            } else {
                subsection.style.display = 'none';
                collapseBtn.textContent = '+';
                section.classList.add('collapsed');
            }
        }

        // 选择场景
        function selectScene(element, sceneName) {
            // 移除其他场景的选中状态
            document.querySelectorAll('.scene-item').forEach(item => {
                item.classList.remove('selected');
            });
            
            // 选中当前场景
            element.classList.add('selected');
            
            console.log('选中场景:', sceneName);
            // 这里可以加载场景详情到右侧面板
            loadSceneDetails(sceneName);
        }

        // 加载场景详情
        function loadSceneDetails(sceneName) {
            // 模拟加载场景详情
            console.log('加载场景详情:', sceneName);
            // 这里可以更新右侧面板显示场景的API列表等信息
        }

        // 编辑场景
        function editScene(sceneName) {
            const newName = prompt('请输入新的场景名称:', sceneName);
            if (newName && newName.trim() && newName !== sceneName) {
                console.log('重命名场景:', sceneName, '->', newName);
                // 更新界面上的场景名称
                const sceneItem = document.querySelector(`[onclick*="${sceneName}"]`);
                if (sceneItem) {
                    sceneItem.querySelector('.scene-item-name').textContent = newName;
                    sceneItem.setAttribute('onclick', sceneItem.getAttribute('onclick').replace(sceneName, newName));
                }
            }
        }

        // 删除场景
        function deleteScene(sceneName) {
            if (confirm(`确定要删除场景 "${sceneName}" 吗？`)) {
                console.log('删除场景:', sceneName);
                // 从界面移除场景项
                const sceneItem = document.querySelector(`[onclick*="${sceneName}"]`);
                if (sceneItem) {
                    sceneItem.remove();
                    updateSceneCounts(-1);
                }
            }
        }

        // 显示场景菜单
        function showSceneMenu(button) {
            console.log('显示场景分组菜单');
            // 这里可以显示一个下拉菜单
        }

        // 显示场景项菜单
        function showSceneItemMenu(button, sceneName) {
            console.log('显示场景项菜单:', sceneName);
            // 这里可以显示一个上下文菜单
        }

        // 更新场景计数
        function updateSceneCounts(change) {
            const subsectionCount = document.querySelector('.subsection-count');
            const totalCount = document.querySelector('.scene-count');
            
            const currentSubCount = parseInt(subsectionCount.textContent);
            const currentTotalCount = parseInt(totalCount.textContent.replace(/[()]/g, ''));
            
            
            subsectionCount.textContent = Math.max(0, currentSubCount + change);
            totalCount.textContent = `(${Math.max(0, currentTotalCount + change)})`;
        }

        // 优化添加场景到列表函数
        function addSceneToList(sceneName) {
            const sceneList = document.querySelector('.scene-subsection');
            const newScene = document.createElement('div');
            newScene.className = 'tree-item scene-item';
            newScene.setAttribute('onclick', `selectScene(this, '${sceneName}')`);
            newScene.innerHTML = `
                <span class="scene-item-name">${sceneName}</span>
                <div class="tree-actions">
                    <button class="action-icon" title="编辑" onclick="event.stopPropagation(); editScene('${sceneName}')">✏</button>
                    <button class="action-icon" title="删除" onclick="event.stopPropagation(); deleteScene('${sceneName}')">🗑</button>
                    <button class="action-icon" title="更多" onclick="event.stopPropagation(); showSceneItemMenu(this, '${sceneName}')">⋯</button>
                </div>
            `;
            sceneList.appendChild(newScene);
            
            // 添加动画效果
            newScene.classList.add('slide-in');
            
            // 更新计数
            updateSceneCounts(1);
        }
    </script>
</body>
</html>
