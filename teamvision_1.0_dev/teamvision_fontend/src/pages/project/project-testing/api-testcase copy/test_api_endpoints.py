#!/usr/bin/env python3
# coding=utf-8
"""
API测试用例功能接口测试脚本

使用方法:
python test_api_endpoints.py --host http://localhost:8000 --project-id 1

Created on 2025-09-15
@author: zhangpeng
"""

import requests
import json
import argparse
import sys


class ApiTestCaseClient:
    """API测试用例客户端"""
    
    def __init__(self, base_url, project_id):
        self.base_url = base_url.rstrip('/')
        self.project_id = project_id
        self.session = requests.Session()
        
    def test_collections(self):
        """测试集合管理接口"""
        print("=== 测试集合管理接口 ===")
        
        # 1. 获取集合列表
        url = f"{self.base_url}/api/project/{self.project_id}/api-test/collections/"
        response = self.session.get(url)
        print(f"GET {url}")
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            print("✓ 获取集合列表成功")
        else:
            print(f"✗ 获取集合列表失败: {response.text}")
            
        # 2. 创建集合
        create_data = {
            "name": "测试集合",
            "description": "这是一个测试集合",
            "is_folder": True,
            "parent": 0
        }
        response = self.session.post(url, json=create_data)
        print(f"POST {url}")
        print(f"Status: {response.status_code}")
        if response.status_code == 201:
            collection_data = response.json()
            collection_id = collection_data.get('id')
            print(f"✓ 创建集合成功，ID: {collection_id}")
            return collection_id
        else:
            print(f"✗ 创建集合失败: {response.text}")
            return None
            
        # 3. 获取集合树
        tree_url = f"{self.base_url}/api/project/{self.project_id}/api-test/collections/tree/"
        response = self.session.get(tree_url)
        print(f"GET {tree_url}")
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            print("✓ 获取集合树成功")
        else:
            print(f"✗ 获取集合树失败: {response.text}")
            
    def test_test_cases(self, collection_id):
        """测试用例管理接口"""
        if not collection_id:
            print("跳过测试用例测试（没有有效的集合ID）")
            return None
            
        print("\n=== 测试用例管理接口 ===")
        
        # 1. 获取测试用例列表
        url = f"{self.base_url}/api/project/{self.project_id}/api-test/cases/"
        response = self.session.get(url)
        print(f"GET {url}")
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            print("✓ 获取测试用例列表成功")
        else:
            print(f"✗ 获取测试用例列表失败: {response.text}")
            
        # 2. 创建测试用例
        create_data = {
            "collection": collection_id,
            "name": "测试API接口",
            "description": "这是一个测试用例",
            "method": "GET",
            "url": "https://jsonplaceholder.typicode.com/posts/1",
            "headers": {
                "Content-Type": "application/json"
            },
            "query_params": {},
            "body_type": "none",
            "body_data": {}
        }
        response = self.session.post(url, json=create_data)
        print(f"POST {url}")
        print(f"Status: {response.status_code}")
        if response.status_code == 201:
            case_data = response.json()
            case_id = case_data.get('id')
            print(f"✓ 创建测试用例成功，ID: {case_id}")
            return case_id
        else:
            print(f"✗ 创建测试用例失败: {response.text}")
            return None
            
    def test_case_execution(self, case_id):
        """测试用例执行接口"""
        if not case_id:
            print("跳过测试用例执行测试（没有有效的用例ID）")
            return
            
        print("\n=== 测试用例执行接口 ===")
        
        # 执行测试用例
        url = f"{self.base_url}/api/project/{self.project_id}/api-test/cases/{case_id}/execute/"
        execute_data = {
            "environment_id": None,
            "override_config": {}
        }
        response = self.session.post(url, json=execute_data)
        print(f"POST {url}")
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print("✓ 执行测试用例成功")
            print(f"  响应状态码: {result.get('result', {}).get('response', {}).get('status_code')}")
            print(f"  响应时间: {result.get('result', {}).get('response', {}).get('response_time')}ms")
        else:
            print(f"✗ 执行测试用例失败: {response.text}")
            
    def test_environments(self):
        """测试环境管理接口"""
        print("\n=== 测试环境管理接口 ===")
        
        # 1. 获取环境列表
        url = f"{self.base_url}/api/project/{self.project_id}/api-test/environments/"
        response = self.session.get(url)
        print(f"GET {url}")
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            print("✓ 获取环境列表成功")
        else:
            print(f"✗ 获取环境列表失败: {response.text}")
            
        # 2. 创建环境
        create_data = {
            "name": "测试环境",
            "description": "这是一个测试环境",
            "variables": {
                "base_url": "https://api.example.com",
                "api_version": "v1"
            },
            "secrets": {
                "api_key": "test-key-123"
            },
            "is_global": False
        }
        response = self.session.post(url, json=create_data)
        print(f"POST {url}")
        print(f"Status: {response.status_code}")
        if response.status_code == 201:
            env_data = response.json()
            env_id = env_data.get('id')
            print(f"✓ 创建环境成功，ID: {env_id}")
            return env_id
        else:
            print(f"✗ 创建环境失败: {response.text}")
            return None
            
    def test_history(self):
        """测试执行历史接口"""
        print("\n=== 测试执行历史接口 ===")
        
        # 获取执行历史
        url = f"{self.base_url}/api/project/{self.project_id}/api-test/history/"
        response = self.session.get(url)
        print(f"GET {url}")
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            print("✓ 获取执行历史成功")
        else:
            print(f"✗ 获取执行历史失败: {response.text}")
            
    def test_code_snippet(self, case_id):
        """测试代码片段生成接口"""
        if not case_id:
            print("跳过代码片段生成测试（没有有效的用例ID）")
            return
            
        print("\n=== 测试代码片段生成接口 ===")
        
        # 生成cURL代码片段
        url = f"{self.base_url}/api/project/{self.project_id}/api-test/cases/{case_id}/code-snippet/"
        snippet_data = {
            "language": "curl",
            "environment_id": None
        }
        response = self.session.post(url, json=snippet_data)
        print(f"POST {url}")
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print("✓ 生成代码片段成功")
            print("代码片段:")
            print(result.get('result', {}).get('code_snippet', ''))
        else:
            print(f"✗ 生成代码片段失败: {response.text}")
            
    def run_all_tests(self):
        """运行所有测试"""
        print(f"开始测试API测试用例功能接口")
        print(f"服务器地址: {self.base_url}")
        print(f"项目ID: {self.project_id}")
        print("=" * 50)
        
        try:
            # 测试集合管理
            collection_id = self.test_collections()
            
            # 测试用例管理
            case_id = self.test_test_cases(collection_id)
            
            # 测试用例执行
            self.test_case_execution(case_id)
            
            # 测试环境管理
            env_id = self.test_environments()
            
            # 测试执行历史
            self.test_history()
            
            # 测试代码片段生成
            self.test_code_snippet(case_id)
            
            print("\n" + "=" * 50)
            print("测试完成！")
            
        except Exception as e:
            print(f"\n测试过程中发生错误: {str(e)}")
            sys.exit(1)


def main():
    parser = argparse.ArgumentParser(description='API测试用例功能接口测试')
    parser.add_argument('--host', default='http://localhost:8000', 
                       help='服务器地址 (默认: http://localhost:8000)')
    parser.add_argument('--project-id', type=int, default=1,
                       help='项目ID (默认: 1)')
    
    args = parser.parse_args()
    
    client = ApiTestCaseClient(args.host, args.project_id)
    client.run_all_tests()


if __name__ == '__main__':
    main()
